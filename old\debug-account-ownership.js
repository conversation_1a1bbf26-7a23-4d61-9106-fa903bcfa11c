/**
 * 🔍 ОТЛАДКА ВЛАДЕНИЯ АККАУНТАМИ
 * Проверяем какой аккаунт вызывает ошибку 3007
 */

const { Connection, PublicKey } = require('@solana/web3.js');

async function debugAccountOwnership() {
    console.log('🔍 ОТЛАДКА ВЛАДЕНИЯ АККАУНТАМИ');
    console.log('='.repeat(60));

    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    const METEORA_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';

    // Аккаунты из транзакции
    const accounts = [
        { name: 'Position', address: '3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP', expectedOwner: METEORA_PROGRAM_ID },
        { name: '<PERSON><PERSON> <PERSON><PERSON>', address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', expectedOwner: METEORA_PROGRAM_ID },
        { name: 'Bitmap Extension', address: '3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C', expectedOwner: METEORA_PROGRAM_ID },
        { name: 'User Token X', address: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk', expectedOwner: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { name: 'User Token Y', address: '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo', expectedOwner: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { name: 'Reserve X', address: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF', expectedOwner: METEORA_PROGRAM_ID },
        { name: 'Reserve Y', address: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD', expectedOwner: METEORA_PROGRAM_ID },
        { name: 'Token X Mint', address: 'So11111111111111111111111111111111111111112', expectedOwner: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { name: 'Token Y Mint', address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', expectedOwner: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { name: 'User', address: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', expectedOwner: '11111111111111111111111111111111' },
        { name: 'Event Authority', address: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6', expectedOwner: '11111111111111111111111111111111' },
        { name: 'Bin Array', address: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', expectedOwner: METEORA_PROGRAM_ID }
    ];

    console.log('\n📊 ПРОВЕРКА ВЛАДЕНИЯ АККАУНТАМИ:');
    console.log('='.repeat(50));

    let problemAccounts = [];

    for (const account of accounts) {
        try {
            console.log(`\n🔍 ${account.name}:`);
            console.log(`   Address: ${account.address}`);
            
            const accountInfo = await connection.getAccountInfo(new PublicKey(account.address));
            
            if (!accountInfo) {
                console.log(`   Status: ❌ НЕ СУЩЕСТВУЕТ`);
                console.log(`   Expected: ${account.expectedOwner}`);
                console.log(`   Problem: Аккаунт должен быть создан`);
                problemAccounts.push({
                    ...account,
                    issue: 'NOT_EXISTS',
                    actualOwner: null
                });
            } else {
                const actualOwner = accountInfo.owner.toBase58();
                const isCorrectOwner = actualOwner === account.expectedOwner;
                
                console.log(`   Status: ✅ СУЩЕСТВУЕТ`);
                console.log(`   Owner: ${actualOwner}`);
                console.log(`   Expected: ${account.expectedOwner}`);
                console.log(`   Match: ${isCorrectOwner ? '✅' : '❌'}`);
                console.log(`   Data: ${accountInfo.data.length} bytes`);
                console.log(`   Lamports: ${accountInfo.lamports}`);
                
                if (!isCorrectOwner) {
                    problemAccounts.push({
                        ...account,
                        issue: 'WRONG_OWNER',
                        actualOwner
                    });
                }
            }
            
        } catch (error) {
            console.log(`   Status: ❌ ОШИБКА`);
            console.log(`   Error: ${error.message}`);
            problemAccounts.push({
                ...account,
                issue: 'RPC_ERROR',
                error: error.message
            });
        }
    }

    // Анализ проблем
    console.log('\n🚨 АНАЛИЗ ПРОБЛЕМ:');
    console.log('='.repeat(40));

    if (problemAccounts.length === 0) {
        console.log('✅ ВСЕ АККАУНТЫ В ПОРЯДКЕ!');
    } else {
        console.log(`❌ НАЙДЕНО ${problemAccounts.length} ПРОБЛЕМНЫХ АККАУНТОВ:`);
        
        problemAccounts.forEach((account, index) => {
            console.log(`\n${index + 1}. ${account.name}:`);
            console.log(`   Address: ${account.address}`);
            console.log(`   Issue: ${account.issue}`);
            
            if (account.issue === 'NOT_EXISTS') {
                console.log(`   🔧 Решение: Аккаунт будет создан автоматически при выполнении add_liquidity2`);
            } else if (account.issue === 'WRONG_OWNER') {
                console.log(`   Expected: ${account.expectedOwner}`);
                console.log(`   Actual: ${account.actualOwner}`);
                console.log(`   🔧 Решение: Проверить правильность PDA формулы или использовать другой аккаунт`);
            } else if (account.issue === 'RPC_ERROR') {
                console.log(`   Error: ${account.error}`);
                console.log(`   🔧 Решение: Проверить RPC подключение`);
            }
        });
    }

    // Специальная проверка для PDA аккаунтов
    console.log('\n🎯 СПЕЦИАЛЬНАЯ ПРОВЕРКА PDA АККАУНТОВ:');
    console.log('='.repeat(45));

    const pdaAccounts = problemAccounts.filter(acc => 
        ['Position', 'Bitmap Extension', 'Reserve X', 'Reserve Y'].includes(acc.name) && 
        acc.issue === 'NOT_EXISTS'
    );

    if (pdaAccounts.length > 0) {
        console.log(`📝 ${pdaAccounts.length} PDA аккаунтов не существуют - это НОРМАЛЬНО для новых позиций`);
        console.log(`✅ Они будут созданы автоматически при выполнении add_liquidity2`);
        
        pdaAccounts.forEach(acc => {
            console.log(`   - ${acc.name}: ${acc.address.slice(0, 8)}...`);
        });
    }

    // Проверка критических проблем
    const criticalProblems = problemAccounts.filter(acc => 
        acc.issue === 'WRONG_OWNER' || 
        (acc.issue === 'NOT_EXISTS' && !['Position', 'Bitmap Extension', 'Reserve X', 'Reserve Y'].includes(acc.name))
    );

    console.log('\n🎯 ИТОГОВЫЙ ДИАГНОЗ:');
    console.log('='.repeat(30));

    if (criticalProblems.length === 0) {
        console.log('✅ НЕТ КРИТИЧЕСКИХ ПРОБЛЕМ');
        console.log('✅ Ошибка 3007 может быть вызвана другими факторами');
        console.log('💡 Возможные причины:');
        console.log('   - Неправильный порядок аккаунтов');
        console.log('   - Неправильные флаги isSigner/isWritable');
        console.log('   - Проблемы с instruction data');
    } else {
        console.log(`❌ НАЙДЕНО ${criticalProblems.length} КРИТИЧЕСКИХ ПРОБЛЕМ:`);
        criticalProblems.forEach(acc => {
            console.log(`   - ${acc.name}: ${acc.issue}`);
        });
    }

    return {
        totalAccounts: accounts.length,
        problemAccounts: problemAccounts.length,
        criticalProblems: criticalProblems.length,
        problems: problemAccounts
    };
}

if (require.main === module) {
    debugAccountOwnership()
        .then(result => {
            console.log(`\n📊 ИТОГО: ${result.problemAccounts}/${result.totalAccounts} проблемных аккаунтов`);
            process.exit(result.criticalProblems > 0 ? 1 : 0);
        })
        .catch(error => {
            console.error('❌ ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { debugAccountOwnership };
