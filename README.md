# 🌪️ BMETEORA BOT - Flash Loan Arbitrage System

## 📋 Описание

**BMETEORA BOT** - это автономная система flash loan арбитража на Solana, специализирующаяся на внутреннем арбитраже между Meteora DLMM пулами с использованием MarginFi займов.

**Цель:** $100,000 за 24 часа через умный арбитраж между 3 Meteora пулами

## 🚀 Быстрый запуск

```bash
# Установка зависимостей
npm install

# Запуск основного бота
node BMETEORA.js
```

## 📁 Структура файлов

### 🔥 Основные файлы
- **BMETEORA.js** - Главный бот арбитража
- **complete-flash-loan-structure.js** - Создание всех 18 инструкций транзакции
- **trading-config.js** - Централизованная конфигурация

### 🚀 Менеджеры
- **meteora-bin-cache-manager-clean.js** - Кэш активных бинов (±1)
- **centralized-rpc-manager.js** - Управление RPC подключениями
- **centralized-amount-converter.js** - Конвертация USD ↔ Native amounts

### 🧠 Анализаторы
- **smart-liquidity-analyzer.js** - Умный анализ ликвидности 3 бинов
- **meteora-fee-analyzer.js** - Анализ реальных комиссий пулов

### 📁 Конфигурация
- **wallet.json** - Приватный ключ кошелька
- **custom-alt-data.json** - ALT таблицы для сжатия транзакций
- **.env.solana** - Переменные окружения (RPC URLs)
- **package.json** - Зависимости и скрипты

## ⚙️ Конфигурация

### Настройка переменных окружения (.env.solana)
```bash
# RPC endpoints
QUICKNODE_RPC_URL=https://your-quicknode-url
HELIUS_RPC_URL=https://your-helius-url

# Wallet
WALLET_PATH=./wallet.json

# MarginFi
MARGINFI_ACCOUNT=your-marginfi-account-address
```

### Основные параметры (trading-config.js)
```javascript
const TRADING_CONFIG = {
  MIN_SPREAD_PERCENT: 0.005,        // 0.05% минимальный спред
  PROTOCOL_FEE_PERCENT: 0.003,      // 0.003% единственная комиссия
  FLASH_LOAN_AMOUNT_USD: 1000,      // $1K минимальная позиция
  MAX_FLASH_LOAN_AMOUNT_USD: 200000, // $200K максимальный займ
  CYCLE_DELAY_MS: 5000,             // 5 сек между циклами
  TRANSACTION_SEND_INTERVAL_MS: 1000 // 1 сек между отправками
};
```

## 🔄 Принцип работы

1. **Мониторинг цен** каждые 600ms через активные бины
2. **Анализ спредов** между пулами (мин. 0.004%)
3. **Умный анализ ликвидности** 3 бинов каждого пула
4. **Создание транзакции** из 18 инструкций с ALT сжатием
5. **Выполнение арбитража** через flash loans

## 📊 Структура транзакции (18 инструкций)

### ComputeBudget (2)
1. `setComputeUnitLimit` - Лимит compute units
2. `setComputeUnitPrice` - Цена compute units

### MarginFi Borrow (2)
3. `lendingAccountBorrow` - Займ USDC
4. `lendingAccountBorrow` - Займ WSOL

### Meteora Swaps (4)
5-8. `swap` - Четыре свопа между пулами

### Meteora Add Liquidity (6)
9-14. `addLiquidityByStrategy` - Добавление ликвидности в 3 бина каждого пула

### MarginFi Repay (4)
15-18. `lendingAccountRepay` - Возврат займов с процентами

## 🛡️ Безопасность

- **Проверка спредов** перед выполнением арбитража
- **Лимиты на размер** позиций и займов
- **Таймауты** для предотвращения зависания
- **Логирование** всех операций для отладки

## 🎯 Ключевые особенности

### ✅ Преимущества
- Автоматический мониторинг цен
- Умный анализ ликвидности только 3 бинов (активный ± 1)
- ALT сжатие транзакций для экономии размера
- Централизованная конфигурация всех параметров
- Защита от rate limiting с интервалами между запросами
- Реальные комиссии каждого пула через анализатор

### 🔧 Технические решения
- Кэширование активных бинов для быстрого доступа к ценам
- Равномерное распределение RPC нагрузки между провайдерами
- Автоматическое восстановление после ошибок
- Динамический расчет оптимальных сумм займов

## 📈 Мониторинг

Бот выводит подробную информацию о:
- Текущих ценах в активных бинах
- Обнаруженных спредах между пулами
- Результатах выполнения арбитража
- Ошибках и их причинах

## 🔧 Техническая поддержка

Для получения подробной информации об архитектуре системы см. файл:
**BMETEORA_ARCHITECTURE_OVERVIEW.md**

## ⚠️ Важные замечания

- Используйте только 3 бина (активный ± 1) для ликвидности
- Комиссия протокола составляет только 0.003%
- Минимальный спред для арбитража: 0.004%
- Система работает только с реальными данными из API и SDK
- Никаких моков, фейков или генераций данных не используется

## 🚀 Статус

Система готова к продакшену и настроена для достижения цели $100,000 за 24 часа через эффективный арбитраж между Meteora DLMM пулами.
