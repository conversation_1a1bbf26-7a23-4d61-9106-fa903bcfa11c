#!/usr/bin/env node

/**
 * ⚡ УЛУЧШЕННАЯ ПАРАЛЛЕЛИЗАЦИЯ ПОЛУЧЕНИЯ ЦЕН
 * 
 * Оптимизирует получение цен с множественных источников
 */

class ParallelPriceFetcher {
  constructor(options = {}) {
    this.options = {
      maxConcurrentRequests: options.maxConcurrentRequests || 10,
      requestTimeout: options.requestTimeout || 3000,
      retryAttempts: options.retryAttempts || 2,
      cacheEnabled: options.cacheEnabled !== false,
      cacheTTL: options.cacheTTL || 10000, // 10 секунд для цен
      ...options
    };
    
    this.priceCache = new Map();
    this.stats = {
      totalRequests: 0,
      cacheHits: 0,
      parallelBatches: 0,
      averageLatency: 0
    };
  }

  /**
   * 🚀 ПАРАЛЛЕЛЬНОЕ ПОЛУЧЕНИЕ ЦЕН ОТ МНОЖЕСТВЕННЫХ ИСТОЧНИКОВ
   */
  async fetchPricesParallel(sources, tokenPairs) {
    const startTime = Date.now();
    
    try {
      console.log(`⚡ Параллельное получение цен: ${sources.length} источников, ${tokenPairs.length} пар`);
      
      // Создаем все возможные комбинации источник-пара
      const requests = [];
      for (const source of sources) {
        for (const pair of tokenPairs) {
          requests.push({
            source,
            pair,
            cacheKey: `${source.name}_${pair.symbol}_${Date.now()}`
          });
        }
      }
      
      // Группируем запросы по батчам для контроля нагрузки
      const batches = this.chunkRequests(requests, this.options.maxConcurrentRequests);
      const allResults = [];
      
      for (const batch of batches) {
        const batchPromises = batch.map(request => this.fetchSinglePrice(request));
        const batchResults = await Promise.allSettled(batchPromises);
        allResults.push(...batchResults);
        
        // Небольшая пауза между батчами для rate limiting
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      // Обрабатываем результаты
      const prices = this.processResults(allResults, sources, tokenPairs);
      
      const latency = Date.now() - startTime;
      this.updateStats(latency);
      
      console.log(`✅ Параллельное получение завершено за ${latency}ms`);
      return prices;
      
    } catch (error) {
      console.error(`❌ Ошибка параллельного получения цен: ${error.message}`);
      throw error;
    }
  }

  /**
   * 📊 ПОЛУЧЕНИЕ ОДНОЙ ЦЕНЫ С КЕШИРОВАНИЕМ
   */
  async fetchSinglePrice(request) {
    const { source, pair, cacheKey } = request;
    
    // Проверяем кеш
    if (this.options.cacheEnabled) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        this.stats.cacheHits++;
        return { success: true, data: cached, source: source.name, pair: pair.symbol };
      }
    }
    
    try {
      // Выполняем запрос с таймаутом
      const pricePromise = source.getPrice(pair);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout')), this.options.requestTimeout)
      );
      
      const result = await Promise.race([pricePromise, timeoutPromise]);
      
      // Сохраняем в кеш
      if (this.options.cacheEnabled && result) {
        this.saveToCache(cacheKey, result);
      }
      
      this.stats.totalRequests++;
      return { success: true, data: result, source: source.name, pair: pair.symbol };
      
    } catch (error) {
      return { success: false, error: error.message, source: source.name, pair: pair.symbol };
    }
  }

  /**
   * 🔧 ГРУППИРОВКА ЗАПРОСОВ ПО БАТЧАМ
   */
  chunkRequests(requests, chunkSize) {
    const chunks = [];
    for (let i = 0; i < requests.length; i += chunkSize) {
      chunks.push(requests.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 📊 ОБРАБОТКА РЕЗУЛЬТАТОВ
   */
  processResults(results, sources, tokenPairs) {
    const prices = {};
    
    // Инициализируем структуру
    for (const pair of tokenPairs) {
      prices[pair.symbol] = {};
    }
    
    // Обрабатываем результаты
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value.success) {
        const { data, source, pair } = result.value;
        if (data && data.price > 0) {
          prices[pair][source] = {
            price: data.price,
            liquidity: data.liquidity || 0,
            timestamp: Date.now()
          };
        }
      }
    }
    
    return prices;
  }

  /**
   * 💾 КЕШИРОВАНИЕ
   */
  getFromCache(key) {
    const cached = this.priceCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.options.cacheTTL) {
      return cached.data;
    }
    return null;
  }

  saveToCache(key, data) {
    this.priceCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 📊 ОБНОВЛЕНИЕ СТАТИСТИКИ
   */
  updateStats(latency) {
    this.stats.parallelBatches++;
    this.stats.averageLatency = 
      (this.stats.averageLatency * (this.stats.parallelBatches - 1) + latency) / 
      this.stats.parallelBatches;
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 ? 
      (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      cacheHitRate: `${hitRate}%`,
      cacheSize: this.priceCache.size
    };
  }
}

module.exports = ParallelPriceFetcher;
