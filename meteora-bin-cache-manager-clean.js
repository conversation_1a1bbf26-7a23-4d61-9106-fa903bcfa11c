const { Connection, PublicKey } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const DLMM = require('@meteora-ag/dlmm').default;

/**
 * 🎯 ЧИСТЫЙ METEORA BIN CACHE MANAGER
 * 
 * ТОЛЬКО НЕОБХОДИМЫЕ МЕТОДЫ:
 * 1. getArbitrageData() - получение цен через getBinsAroundActiveBin(1,1) - ТОЛЬКО 3 БИНА: АКТИВНЫЙ ± 1!
 * 2. createFastSwap() - создание swap транзакций через getBinArrayForSwap() + swap()
 * 3. Кэширование DLMM инстансов
 * 
 * УДАЛЕНО:
 * - Автообновление каждые 900ms
 * - Старые методы парсинга
 * - Дублирующие запросы
 * - Лишние setInterval
 */
class MeteoraBinCacheManager {
    constructor() {
        console.log('🚀 Meteora Active Bin Cache Manager инициализирован');

        // 💾 КЭШИ
        this.dlmmInstancesCache = new Map(); // Кэш DLMM инстансов
        this.binArraysCache = new Map(); // 🔥 КЭШ РЕАЛЬНЫХ BIN ARRAYS!
        this.existingBinArraysCache = new Map(); // 🔥 КЭШ ВСЕХ СУЩЕСТВУЮЩИХ BIN ARRAYS!

        // ⏱️ НАСТРОЙКИ КЭШИРОВАНИЯ
        this.ACTIVE_BIN_CACHE_DURATION = 60000; // 60 секунд

        // 🔥 НОВЫЕ НАСТРОЙКИ ДЛЯ СТАБИЛЬНОГО ОБНОВЛЕНИЯ
        this.autoUpdateInterval = null;
        this.isUpdating = false;
        this.pools = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // POOL_1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'  // POOL_2
        ];

        // 🔧 НАСТРОЙКИ ПОДКЛЮЧЕНИЯ
        this.connection = null; // Будет установлено при первом использовании



        // 🔥 ЕДИНСТВЕННАЯ СИСТЕМА АВТООБНОВЛЕНИЯ
        this.autoUpdateInterval = null;
        this.isUpdating = false;

        // 🔥 METEORA DLMM PROGRAM ID
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

        // 🔥 CALLBACK ДЛЯ СИНХРОНИЗАЦИИ С АНАЛИЗАТОРОМ
        this.onCacheUpdated = null; // Будет установлен из BMETEORA.js

        // 🔥 АВТООБНОВЛЕНИЕ БУДЕТ ЗАПУЩЕНО ПОСЛЕ ПОЛНОЙ ИНИЦИАЛИЗАЦИИ!
        // НЕ ЗАПУСКАЕМ СРАЗУ - ЖДЕМ ГОТОВНОСТИ RPC ПОДКЛЮЧЕНИЙ!
        console.log('🔧 Кэш-менеджер создан, автообновление будет запущено после инициализации RPC');
    }

    /**
     * 🌐 HELIUS ПОДКЛЮЧЕНИЕ ИЗ .env.solana КОНФИГУРАЦИИ!
     */
    async getRPCForOperation(operation) {
        // 🔥 HELIUS RPC ИЗ .env.solana - ТОЛЬКО ДЛЯ КЭША!
        if (!this.connection) {
            console.log('🔥 СОЗДАЕМ HELIUS ПОДКЛЮЧЕНИЕ ИЗ .env.solana ДЛЯ КЭША!');

            // 🔥 ЗАГРУЖАЕМ .env.solana!
            require('dotenv').config({ path: '.env.solana' });

            // 🔥 БЕРЕМ HELIUS URL ИЗ КОНФИГУРАЦИИ!
            const heliusUrl = process.env.HELIUS_RPC_URL;
            if (!heliusUrl) {
                throw new Error('❌ HELIUS_RPC_URL не найден в .env.solana!');
            }

            this.connection = new Connection(heliusUrl, {
                commitment: 'confirmed',
                confirmTransactionInitialTimeout: 30000,
                disableRetryOnRateLimit: false // Разрешаем retry для кэша
            });

            console.log('✅ HELIUS ПОДКЛЮЧЕНИЕ ДЛЯ КЭША СОЗДАНО ИЗ .env.solana!');
        }

        // 🔥 ВСЕ ОПЕРАЦИИ КЭША ИДУТ ТОЛЬКО ЧЕРЕЗ HELIUS!
        console.log(`🌐 Подключение к HELIUS: ${this.connection.rpcEndpoint.slice(0, 50)}...`);
        return this.connection;
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ СТРОКОВОГО ПРЕДСТАВЛЕНИЯ АДРЕСА ПУЛА
     */
    getPoolStr(poolAddress) {
        if (!poolAddress) {
            throw new Error(`🚨 КРИТИЧЕСКАЯ ОШИБКА: poolAddress не передан в getPoolStr! Получен: ${poolAddress}`);
        }
        return typeof poolAddress === 'string' ? poolAddress : poolAddress.toString();
    }

    // 🔥 БЕЗОПАСНЫЙ RPC ЗАПРОС С RATE LIMITING
    async safeRpcCall(rpcFunction, ...args) {
        // Проверяем rate limit
        const now = Date.now();
        const timeSinceLastCall = now - this.lastRpcCall;

        if (timeSinceLastCall < this.minRpcDelay) {
            const waitTime = this.minRpcDelay - timeSinceLastCall;
            console.log(`⏳ Rate limiting: ждем ${waitTime}ms перед RPC запросом...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        // Сброс счетчика каждую минуту
        if (now - this.rpcResetTime > 60000) {
            this.rpcCallCount = 0;
            this.rpcResetTime = now;
        }

        this.rpcCallCount++;
        this.lastRpcCall = Date.now();

        try {
            console.log(`📡 RPC запрос #${this.rpcCallCount} (rate limited)...`);
            const result = await rpcFunction(...args);
            return result;
        } catch (error) {
            if (error.message.includes('429') || error.message.includes('rate limited')) {
                console.log(`🚨 RATE LIMIT ПРЕВЫШЕН! Ждем 5 секунд...`);
                await new Promise(resolve => setTimeout(resolve, 5000)); // Увеличено до 5 секунд

                // Повторяем запрос после задержки
                console.log(`🔄 Повторяем RPC запрос после rate limit...`);

                // Дополнительная задержка перед повтором
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await rpcFunction(...args);
            }
            throw error;
        }
    }

    /**
     * 🚀 ПРОДАКШЕН: ПОЛУЧЕНИЕ PDA С КЕШИРОВАНИЕМ
     */
    getRequiredBinArrays(poolAddress, targetBinIndexes) {
        const poolStr = poolAddress.toString();

        // 🎯 ВЫЧИСЛЯЕМ УНИКАЛЬНЫЕ CHUNK INDEXES
        const chunkIndexes = [...new Set(targetBinIndexes.map(binId => Math.floor(binId / 64)))];
        const binArrayPDAs = [];

        for (const chunkIndex of chunkIndexes) {
            const cacheKey = `${poolStr}_${chunkIndex}`;
            const now = Date.now();

            // 🔥 ПРОВЕРЯЕМ КЭШ PDA
            const cached = this.pdaCache.get(cacheKey);
            if (cached && (now - cached.timestamp) < this.pdaCacheTimeout) {
                binArrayPDAs.push(cached.pda);
                continue;
            }

            // 🔥 ГЕНЕРИРУЕМ PDA С ПРАВИЛЬНЫМ ФОРМАТОМ
            const chunkBuffer = Buffer.alloc(8);
            chunkBuffer.writeBigInt64LE(BigInt(chunkIndex));

            const [pda] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    new PublicKey(poolStr).toBuffer(),
                    chunkBuffer
                ],
                new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')
            );

            // 🔥 СОХРАНЯЕМ В КЭШ
            this.pdaCache.set(cacheKey, { pda, timestamp: now });
            binArrayPDAs.push(pda);
        }

        return { chunkIndexes, binArrayPDAs };
    }

    /**
     * 🚀 ПРОДАКШЕН: ПРОВЕРКА СУЩЕСТВОВАНИЯ АККАУНТОВ С КЕШИРОВАНИЕМ
     */
    async checkAccountsExist(connection, pdas) {
        const results = [];
        const uncachedPDAs = [];
        const uncachedIndexes = [];
        const now = Date.now();

        // 🔥 ПРОВЕРЯЕМ КЭШ СУЩЕСТВОВАНИЯ
        for (let i = 0; i < pdas.length; i++) {
            const pdaStr = pdas[i].toString();
            const cached = this.accountExistsCache.get(pdaStr);

            if (cached && (now - cached.timestamp) < this.accountExistsCacheTimeout) {
                results[i] = cached.exists;
            } else {
                uncachedPDAs.push(pdas[i]);
                uncachedIndexes.push(i);
            }
        }

        // 🔥 ПРОВЕРЯЕМ НЕКЕШИРОВАННЫЕ АККАУНТЫ
        if (uncachedPDAs.length > 0) {
            const accounts = await connection.getMultipleAccountsInfo(uncachedPDAs);

            for (let j = 0; j < accounts.length; j++) {
                const exists = accounts[j] !== null;
                const originalIndex = uncachedIndexes[j];
                const pdaStr = uncachedPDAs[j].toString();

                results[originalIndex] = exists;

                // 🔥 СОХРАНЯЕМ В КЭШ
                this.accountExistsCache.set(pdaStr, { exists, timestamp: now });
            }
        }

        return results;
    }

    /**
     * 🎯 ГЛАВНЫЙ МЕТОД: ПОЛУЧЕНИЕ ДАННЫХ ДЛЯ АРБИТРАЖА
     * Один RPC запрос на пул через getBinsAroundActiveBin(1,1) - ТОЛЬКО 3 БИНА: АКТИВНЫЙ ± 1!
     * Возвращает цены + готовые DLMM инстансы для создания транзакций
     */
    async getArbitrageData(poolAddresses) {
        const startTime = Date.now();

        try {
            if (!this.arbitrageMode) {
                console.log(`🎯 ПОЛУЧЕНИЕ ДАННЫХ ДЛЯ АРБИТРАЖА: ${poolAddresses.length} пулов`);
            }

            if (poolAddresses.length === 0) return [];

            // 🔥 ПОЛУЧАЕМ CONNECTION ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР!
            const connection = await this.getRPCForOperation('meteora');
            console.log(`🔥 ИСПОЛЬЗУЕМ RPC ДЛЯ METEORA ОПЕРАЦИЙ`);

            // 🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache ПАРАЛЛЕЛЬНО!
            console.log(`🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache...`);

            // 🚀 ПОСЛЕДОВАТЕЛЬНАЯ ОБРАБОТКА ПУЛОВ С ЗАДЕРЖКОЙ!
            const poolResults = [];

            for (let i = 0; i < poolAddresses.length; i++) {
                const poolAddress = poolAddresses[i];
                const poolStr = this.getPoolStr(poolAddress);
                const shortPoolAddress = poolStr.slice(0, 8);
                const poolPubkey = new PublicKey(poolStr);

                try {
                    console.log(`🔄 Обрабатываем пул ${i + 1}/${poolAddresses.length}: ${shortPoolAddress}...`);

                    // 🔥 СНАЧАЛА СОЗДАЕМ ОБЫЧНЫЙ DLMM ДЛЯ ПОЛУЧЕНИЯ ДАННЫХ!
                    console.log(`🔍 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache...`);
                    const dlmmPool = await DLMM.create(connection, poolPubkey);

                    // 🔥 ПОЛУЧАЕМ ТОЛЬКО АКТИВНЫЙ БИН (БЕЗ СОСЕДНИХ)!
                    console.log(`🔥 ВЫЗЫВАЕМ getBinsAroundActiveBin(0, 0) для получения ТОЛЬКО АКТИВНОГО БИНА...`);

                    let activeBinData = null;
                    let activeBinParsed = null; // 🔥 ОБЪЯВЛЯЕМ В ПРАВИЛЬНОЙ ОБЛАСТИ ВИДИМОСТИ!
                    let realActiveBin = dlmmPool.lbPair.activeId;

                    try {
                        const { activeBin, bins } = await dlmmPool.getBinsAroundActiveBin(0, 0);

                        if (!bins || bins.length === 0) {
                            throw new Error('Получен пустой массив бинов');
                        }

                        console.log(`✅ ПОЛУЧЕНО ${bins.length} РЕАЛЬНЫХ БИНОВ ИЗ API!`);

                        // 🔥 БЕРЕМ ТОЛЬКО АКТИВНЫЙ БИН!
                        const activeBinIndex = bins.findIndex(bin => bin.binId === realActiveBin);
                        if (activeBinIndex === -1) {
                            throw new Error(`Активный бин ${realActiveBin} не найден в полученных бинах`);
                        }

                        // Берем только активный бин
                        activeBinData = bins[activeBinIndex];

                        // 🔥 ПАРСИМ ТОЛЬКО АКТИВНЫЙ БИН С РЕАЛЬНЫМИ ЦЕНАМИ И ЛИКВИДНОСТЬЮ!
                        activeBinParsed = {
                            binId: activeBinData.binId,
                            price: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                            pricePerToken: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                            isActive: true,
                            // 🔥 ДОБАВЛЯЕМ РЕАЛЬНУЮ ЛИКВИДНОСТЬ ИЗ API!
                            xAmount: activeBinData.xAmount || 0,           // WSOL amount (native)
                            yAmount: activeBinData.yAmount || 0,           // USDC amount (native)
                            supply: activeBinData.supply || 0,             // LP token supply
                            liquidityX: activeBinData.xAmount || 0,        // WSOL liquidity (native)
                            liquidityY: activeBinData.yAmount || 0         // USDC liquidity (native)
                        };

                        console.log(`🔥 АКТИВНЫЙ БИН С ЦЕНОЙ:`);
                        console.log(`   Бин ${activeBinParsed.binId}: цена ${activeBinParsed.price} (АКТИВНЫЙ)`);

                    } catch (error) {
                        console.log(`❌ ОШИБКА getBinsAroundActiveBin: ${error.message}`);

                        // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПОЛУЧАЕМ РЕАЛЬНЫЙ АКТИВНЫЙ БИН ИЗ RPC!
                        console.log(`🔥 FALLBACK: Получаем реальный активный бин из RPC...`);

                        try {
                            // Получаем реальный активный бин напрямую из блокчейна
                            const accountInfo = await this.safeRpcCall(
                                connection.getAccountInfo.bind(connection),
                                poolPubkey
                            );

                            if (accountInfo && accountInfo.data) {
                                // Парсим активный бин из данных аккаунта (offset 72)
                                const realActiveBinFromRpc = accountInfo.data.readInt32LE(72);
                                console.log(`✅ РЕАЛЬНЫЙ АКТИВНЫЙ БИН ИЗ RPC: ${realActiveBinFromRpc}`);

                                // Создаем 3 бина вокруг реального активного бина
                                threeBins = [
                                    { binId: realActiveBinFromRpc - 1, price: 0, isActive: false },
                                    { binId: realActiveBinFromRpc, price: 0, isActive: true },
                                    { binId: realActiveBinFromRpc + 1, price: 0, isActive: false }
                                ];
                                realActiveBin = realActiveBinFromRpc;

                                console.log(`🔥 СОЗДАЛИ 3 БИНА ВОКРУГ РЕАЛЬНОГО АКТИВНОГО БИНА ${realActiveBin}`);
                            } else {
                                throw new Error('Не удалось получить данные аккаунта пула');
                            }
                        } catch (rpcError) {
                            console.log(`❌ ОШИБКА RPC FALLBACK: ${rpcError.message}`);
                            console.log(`🔥 ПОСЛЕДНИЙ FALLBACK: Используем кэшированные данные...`);

                            const cachedData = this.binArraysCache.get(poolStr);
                            if (cachedData && cachedData.threeBins && cachedData.threeBins.length > 0) {
                                const cacheAge = Date.now() - cachedData.timestamp;
                                console.log(`⚠️ ИСПОЛЬЗУЕМ УСТАРЕВШИЕ КЭШИРОВАННЫЕ ДАННЫЕ (возраст: ${Math.round(cacheAge/1000)}с)`);

                                threeBins = cachedData.threeBins;
                                realActiveBin = cachedData.activeBinId;
                            } else {
                                console.log(`❌ НЕТ ДАННЫХ - ПРОПУСКАЕМ ПУЛ!`);
                                return null;
                            }
                        }
                    }

                    const activeBinPrice = activeBinParsed ? activeBinParsed.price : 0;

                    // 🔥 ПРОВЕРЯЕМ ЧТО activeBinParsed ОПРЕДЕЛЕН
                    if (!activeBinParsed) {
                        throw new Error('activeBinParsed не определен - не удалось получить данные активного бина');
                    }

                    // 🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ BIN ARRAYS ЧЕРЕЗ API!
                    console.log(`🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ BIN ARRAYS ДЛЯ АКТИВНОГО БИНА...`);

                    let ourBinArrayAddresses = [];

                    try {
                        // 🚀 ПРОДАКШЕН: ПОЛУЧАЕМ BIN ARRAYS С КЕШИРОВАНИЕМ!
                        console.log(`🚀 ПОЛУЧАЕМ BIN ARRAYS С КЕШИРОВАНИЕМ (ПРОДАКШЕН ВЕРСИЯ)...`);

                        const activeBinId = realActiveBin;
                        console.log(`   Активный бин ID: ${activeBinId}`);

                        // 🎯 ПОЛУЧАЕМ CHUNK INDEX ДЛЯ АКТИВНОГО БИНА!
                        const currentActiveBinId = activeBinParsed.binId;
                        console.log(`   Активный бин: ${currentActiveBinId}`);

                        // 🔥 ВЫЧИСЛЯЕМ CHUNK ДЛЯ АКТИВНОГО БИНА!
                        const chunkIndex = Math.floor(currentActiveBinId / 64);
                        const chunkIndexes = [chunkIndex];
                        console.log(`   Chunk index для активного бина: ${chunkIndex}`);

                        if (chunkIndexes.length === 0) {
                            throw new Error('Не удалось вычислить chunk indexes для наших бинов');
                        }

                        console.log(`   Chunk indexes: ${chunkIndexes.join(', ')}`);

                        // 🎯 СОЗДАЕМ PDA ДЛЯ КАЖДОГО CHUNK
                        const binArrayPDAs = chunkIndexes.map(chunkIndex => {
                            console.log(`   Создаем PDA для chunk ${chunkIndex}...`);

                            // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ ФОРМАТ ДЛЯ METEORA DLMM (BigInt64LE)!
                            const seedChunk = Buffer.alloc(8); // 8 байт для BigInt64LE!
                            seedChunk.writeBigInt64LE(BigInt(chunkIndex), 0); // BigInt64LE работает!

                            const [pda] = PublicKey.findProgramAddressSync(
                                [
                                    Buffer.from('bin_array'),
                                    new PublicKey(poolStr).toBuffer(),
                                    seedChunk
                                ],
                                new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')
                            );

                            console.log(`   Chunk ${chunkIndex} → PDA: ${pda.toString().slice(0,8)}`);
                            return pda;
                        });

                        console.log(`   Проверяем PDA: ${binArrayPDAs.map(p => p.toString().slice(0,8)).join(', ')}`);

                        // 🔥 БЕЗОПАСНАЯ ПРОВЕРКА BIN ARRAYS С RATE LIMITING!
                        let accounts = null;

                        try {
                            console.log(`   📡 Безопасный RPC запрос для проверки BinArrays...`);
                            accounts = await this.safeRpcCall(
                                connection.getMultipleAccountsInfo.bind(connection),
                                binArrayPDAs
                            );
                            console.log(`   ✅ RPC запрос успешен с rate limiting`);
                        } catch (rpcError) {
                            console.log(`   ❌ RPC ошибка: ${rpcError.message}`);
                            console.log(`   🔥 FALLBACK: Используем кэшированные данные без проверки существования!`);
                            // Предполагаем что все PDA существуют (используем кэш)
                            accounts = binArrayPDAs.map(() => ({ exists: true })); // Фейковые данные для fallback
                        }

                        const existingBinArrays = [];

                        accounts.forEach((account, index) => {
                            const pda = binArrayPDAs[index];
                            const chunkIndex = chunkIndexes[index];
                            const exists = account !== null;

                            // 🔥 ПОКАЗЫВАЕМ КАКИЕ БИНЫ В ЭТОМ CHUNK'Е (ИСПОЛЬЗУЕМ АКТИВНЫЙ БИН)
                            const binsInChunk = [currentActiveBinId].filter(binId => Math.floor(binId / 64) === chunkIndex);

                            console.log(`   Chunk ${chunkIndex} (${pda.toString().slice(0,8)}): ${exists ? '✅ СУЩЕСТВУЕТ' : '❌ НЕ СУЩЕСТВУЕТ'} - бины: ${binsInChunk.join(', ')}`);

                            if (exists) {
                                existingBinArrays.push(pda);
                            } else {
                                console.log(`   ⚠️ CHUNK ${chunkIndex} НЕ СУЩЕСТВУЕТ! Бины ${binsInChunk.join(', ')} не смогут быть обработаны!`);
                            }
                        });

                        if (existingBinArrays.length > 0) {
                            ourBinArrayAddresses = existingBinArrays.map(pda => pda.toString());
                            console.log(`✅ НАЙДЕНО ${ourBinArrayAddresses.length} СУЩЕСТВУЮЩИХ BIN ARRAYS!`);
                            console.log(`   Bin Arrays: ${ourBinArrayAddresses.map(addr => addr.slice(0,8)).join(', ')}`);
                        } else {
                            throw new Error('НЕТ СУЩЕСТВУЮЩИХ BIN ARRAYS! Даже активный chunk не найден!');
                        }

                    } catch (error) {
                        console.log(`⚠️ Ошибка получения bin arrays через chunks: ${error.message}`);
                        console.log(`🔄 ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЕ BIN ARRAYS ИЗ КЭША...`);

                        // 🔥 ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЕ BIN ARRAYS ИЗ НАШЕГО КЭША!
                        const existingBinArrays = this.existingBinArraysCache.get(poolStr);

                        if (existingBinArrays && existingBinArrays.length > 0) {
                            // Берем первые доступные BinArray
                            ourBinArrayAddresses = existingBinArrays.slice(0, 2).map(ba => ba.address);
                            console.log(`✅ ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЕ BIN ARRAYS ИЗ КЭША: ${ourBinArrayAddresses.length} шт.`);
                            ourBinArrayAddresses.forEach((addr, index) => {
                                console.log(`   [${index}] ${addr.slice(0,8)}...`);
                            });
                        } else {
                            console.log(`❌ НЕТ СУЩЕСТВУЮЩИХ BIN ARRAYS В КЭШЕ ДЛЯ ПУЛА ${poolStr.slice(0,8)}!`);
                            console.log(`🔥 ПРИНУДИТЕЛЬНО ГЕНЕРИРУЕМ BIN ARRAY PDA ДЛЯ АКТИВНОГО БИНА!`);

                            // 🔥 ПРИНУДИТЕЛЬНАЯ ГЕНЕРАЦИЯ BIN ARRAY PDA ДЛЯ АКТИВНОГО БИНА
                            const chunkIndex = Math.floor(currentActiveBinId / 64);
                            const indexBuffer = Buffer.alloc(8);
                            indexBuffer.writeBigInt64LE(BigInt(chunkIndex), 0);

                            const seeds = [
                                Buffer.from("bin_array"),
                                poolPubkey.toBuffer(),
                                indexBuffer
                            ];

                            const [binArrayPDA] = PublicKey.findProgramAddressSync(seeds, this.METEORA_DLMM_PROGRAM);
                            ourBinArrayAddresses = [binArrayPDA.toString()];

                            console.log(`✅ ПРИНУДИТЕЛЬНО СГЕНЕРИРОВАН BIN ARRAY PDA: ${binArrayPDA.toString().slice(0,8)}... для chunk ${chunkIndex}`);
                            console.log(`🔥 АКТИВНЫЙ БИН ${currentActiveBinId} → CHUNK ${chunkIndex} → PDA ${binArrayPDA.toString().slice(0,8)}...`);
                        }
                    }

                    // 🔥 ИСПРАВЛЕНИЕ: СОЗДАЕМ ДОПОЛНИТЕЛЬНЫЕ BIN ARRAYS ДЛЯ СОСЕДНИХ ЧАНКОВ
                    const additionalBinArrays = [];

                    // Добавляем BinArray для соседних чанков (±1 от активного)
                    const neighborChunks = [chunkIndex - 1, chunkIndex + 1];
                    for (const neighborChunk of neighborChunks) {
                        const neighborIndexBuffer = Buffer.alloc(8);
                        neighborIndexBuffer.writeBigInt64LE(BigInt(neighborChunk));

                        const neighborSeeds = [
                            Buffer.from('bin_array'),
                            new PublicKey(poolStr).toBuffer(),
                            neighborIndexBuffer
                        ];

                        const [neighborBinArrayPDA] = PublicKey.findProgramAddressSync(neighborSeeds, this.METEORA_DLMM_PROGRAM);
                        additionalBinArrays.push(neighborBinArrayPDA.toString());
                        console.log(`✅ ДОПОЛНИТЕЛЬНЫЙ BIN ARRAY для chunk ${neighborChunk}: ${neighborBinArrayPDA.toString().slice(0,8)}...`);
                    }

                    const binArraysCacheData = {
                        activeBinId: realActiveBin, // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ АКТИВНЫЙ БИН!
                        activeBinPrice: activeBinParsed.price,
                        activeBin: activeBinParsed, // 🔥 ТОЛЬКО АКТИВНЫЙ БИН С РЕАЛЬНЫМИ ДАННЫМИ!
                        binArrays: [...ourBinArrayAddresses, ...additionalBinArrays], // 🔥 ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ ДОПОЛНИТЕЛЬНЫЕ BIN ARRAYS!
                        poolAddress: poolStr, // 🔥 ДОБАВЛЯЕМ АДРЕС ПУЛА ДЛЯ АНАЛИЗАТОРА!
                        timestamp: Date.now()
                    };

                    this.binArraysCache.set(poolStr, binArraysCacheData);

                    // 🔥 ДИАГНОСТИКА: ЧТО ИМЕННО СОХРАНИЛИ В КЭШ
                    console.log(`🔥 ДИАГНОСТИКА СОХРАНЕНИЯ В КЭШ ДЛЯ ${poolStr.slice(0,8)}:`);
                    console.log(`   activeBinId: ${binArraysCacheData.activeBinId}`);
                    console.log(`   activeBin: ${binArraysCacheData.activeBin ? 'ЕСТЬ' : 'НЕТ'}`);
                    console.log(`   binArrays: ${binArraysCacheData.binArrays.length} шт. (МАССИВ)`);
                    console.log(`   binArrays тип: ${Array.isArray(binArraysCacheData.binArrays) ? 'МАССИВ' : typeof binArraysCacheData.binArrays}`);
                    console.log(`   timestamp: ${binArraysCacheData.timestamp}`);

                    // 🔥 СОХРАНЯЕМ DLMM INSTANCE В КЭШ ДЛЯ getDLMMInstance()!
                    this.dlmmInstancesCache.set(poolStr, dlmmPool);

                    console.log(`✅ АКТИВНЫЙ БИН ПОЛУЧЕН ДЛЯ ${shortPoolAddress}: ${activeBinParsed ? activeBinParsed.binId : 'НЕТ'}`);
                    console.log(`✅ DLMM INSTANCE СОХРАНЕН В КЭШ ДЛЯ ${shortPoolAddress}`);

                    poolResults.push({ status: 'fulfilled', value: { poolAddress: poolStr, success: true } });

                } catch (error) {
                    console.log(`❌ Ошибка получения 3 бинов для ${poolStr.slice(0,8)}: ${error.message}`);
                    poolResults.push({ status: 'rejected', reason: error });
                }

                // Задержка между пулами для rate limiting
                if (i < poolAddresses.length - 1) {
                    console.log(`⏳ Задержка 2000ms перед следующим пулом...`);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Увеличено до 2 секунд
                }
            }

            // 🚀 ПОСЛЕДОВАТЕЛЬНОЕ ОБНОВЛЕНИЕ ЗАВЕРШЕНО!
            console.log(`🚀 ПОСЛЕДОВАТЕЛЬНОЕ ОБНОВЛЕНИЕ ${poolAddresses.length} ПУЛОВ ЗАВЕРШЕНО`);

            let successCount = 0;
            poolResults.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value && result.value.success) {
                    successCount++;
                } else {
                    console.log(`❌ Пул ${poolAddresses[index].slice(0,8)} не обновлен: ${result.reason || 'неизвестная ошибка'}`);
                }
            });

            console.log(`✅ ПОСЛЕДОВАТЕЛЬНОЕ ОБНОВЛЕНИЕ ЗАВЕРШЕНО: ${successCount}/${poolAddresses.length} пулов обновлено`);

            const results = [];
            // connection уже объявлен выше

            // 🔥 ОБРАБАТЫВАЕМ КАЖДЫЙ ПУЛ - ПОЛУЧАЕМ ЦЕНЫ ИЗ КЭША!
            for (const poolAddress of poolAddresses) {
                try {
                    const poolStr = this.getPoolStr(poolAddress);
                    const poolPubkey = new PublicKey(poolStr);

                    if (!this.arbitrageMode) {
                        console.log(`🔥 ИСПОЛЬЗУЕМ НАШИ 3 БИНА ИЗ КЭША: ${poolStr.slice(0, 8)}...`);
                    }

                    // 🔥 ИСПОЛЬЗУЕМ НАШИ РЕАЛЬНЫЕ БИНЫ ИЗ binArraysCache!
                    let cacheData = this.binArraysCache.get(poolStr);

                    if (!cacheData || !cacheData.threeBins || cacheData.threeBins.length === 0) {
                        console.log(`⚠️ НЕТ БИНОВ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}! Пытаемся обновить...`);

                        // 🔥 ПОСЛЕДНЯЯ ПОПЫТКА: ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ ОДНОГО ПУЛА!
                        try {
                            console.log(`🔄 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ ПУЛА ${poolStr.slice(0, 8)}...`);
                            await this.updatePool(poolAddress); // 🔥 ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ updatePool ВМЕСТО updateActiveBin

                            // Проверяем кэш еще раз
                            cacheData = this.binArraysCache.get(poolStr);
                            if (cacheData && cacheData.threeBins && cacheData.threeBins.length > 0) {
                                console.log(`✅ ПУЛ ${poolStr.slice(0, 8)} УСПЕШНО ОБНОВЛЕН ПРИНУДИТЕЛЬНО!`);
                            } else {
                                console.log(`❌ ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ НЕ ПОМОГЛО для ${poolStr.slice(0, 8)} - ПРОПУСКАЕМ!`);
                                continue;
                            }
                        } catch (updateError) {
                            console.log(`❌ ОШИБКА ПРИНУДИТЕЛЬНОГО ОБНОВЛЕНИЯ ${poolStr.slice(0, 8)}: ${updateError.message}`);
                            continue;
                        }
                    }

                    console.log(`✅ НАЙДЕНО ${cacheData.threeBins.length} БИНОВ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}`);

                    // 🔥 ИСПОЛЬЗУЕМ НАШИ 3 БИНА ВМЕСТО SDK!
                    const activeBin = cacheData.threeBins[1].binId; // Средний бин
                    const bins = cacheData.threeBins; // Наши 3 бина

                    if (!bins || bins.length !== 3) {
                        console.log(`❌ НЕТ 3 БИНОВ ДЛЯ ${poolStr.slice(0, 8)}`);
                        continue;
                    }

                    // 🔥 ИСПОЛЬЗУЕМ ЦЕНУ ИЗ СРЕДНЕГО БИНА (АКТИВНЫЙ БИН)!
                    const activeBinData = bins[1]; // Средний бин из 3
                    let activePrice = activeBinData ? activeBinData.price : 0;

                    // 🔥 FALLBACK: ЕСЛИ ЦЕНА НУЛЕВАЯ, ПОЛУЧАЕМ ИЗ DLMM!
                    if (!activePrice || activePrice === 0) {
                        console.log(`⚠️ ЦЕНА НУЛЕВАЯ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}, ПОЛУЧАЕМ ИЗ DLMM...`);
                        try {
                            const poolPubkey = new PublicKey(poolStr);
                            const connection = await this.getRPCForOperation('meteora');
                            const dlmmPool = await DLMM.create(connection, poolPubkey);

                            // 🔍 ДИАГНОСТИКА: ПРОВЕРЯЕМ ВСЕ ПОЛЯ DLMM!
                            console.log(`🔍 ДИАГНОСТИКА DLMM ДЛЯ ${poolStr.slice(0,8)}:`);
                            console.log(`   activeId: ${dlmmPool.lbPair.activeId}`);
                            console.log(`   activePrice: ${dlmmPool.lbPair.activePrice}`);
                            console.log(`   binStep: ${dlmmPool.lbPair.binStep}`);
                            console.log(`   baseFactor: ${dlmmPool.lbPair.baseFactor}`);

                            // 🔥 ВЫЧИСЛЯЕМ ЦЕНУ ИЗ activeId И binStep!
                            const activeId = dlmmPool.lbPair.activeId;
                            const binStep = dlmmPool.lbPair.binStep;

                            if (activeId !== undefined && binStep !== undefined) {
                                // Формула Meteora DLMM: price = (1 + binStep / 10000) ** activeId
                                activePrice = Math.pow(1 + binStep / 10000, activeId);
                                console.log(`🔥 ВЫЧИСЛЕНА ЦЕНА: (1 + ${binStep}/10000)^${activeId} = ${activePrice}`);
                            } else {
                                activePrice = 0;
                                console.log(`❌ НЕ УДАЛОСЬ ВЫЧИСЛИТЬ ЦЕНУ: activeId=${activeId}, binStep=${binStep}`);
                            }

                            console.log(`✅ ИТОГОВАЯ ЦЕНА: ${activePrice}`);
                        } catch (error) {
                            console.log(`❌ НЕ УДАЛОСЬ ПОЛУЧИТЬ ЦЕНУ ИЗ DLMM: ${error.message}`);
                            continue;
                        }
                    }

                    if (!activePrice || activePrice === 0) {
                        console.log(`❌ НЕТ ЦЕНЫ В СРЕДНЕМ БИНЕ ДЛЯ ${poolStr.slice(0, 8)}`);
                        continue;
                    }

                    if (!this.arbitrageMode) {
                        const age = Date.now() - Date.now(); // Возраст = 0 для свежих данных
                        const poolKey = poolStr === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6' ? 'meteora1' : 'meteora2';
                        console.log(`✅ ${poolKey}: $${activePrice.toFixed(4)} (бин ID: ${activeBin}, возраст: ${age}ms)`);
                    }

                    // 🔥 ПОЛУЧАЕМ DLMM INSTANCE ИЗ КЭША!
                    const dlmmInstance = this.dlmmInstancesCache.get(poolStr);

                    // 🔥 СОХРАНЯЕМ ДАННЫЕ С DLMM INSTANCE!
                    const poolData = {
                        poolAddress: poolStr,
                        activeBinId: activeBin,
                        activeBinPrice: activePrice,
                        dlmmInstance: dlmmInstance, // 🔥 ИСПОЛЬЗУЕМ DLMM ИЗ КЭША!
                        bins: bins, // 🔥 НАШИ 3 БИНА ИЗ КЭША!
                        readyForSwap: true, // 🔥 ГОТОВЫ БЕЗ RPC ЗАПРОСОВ!
                        timestamp: cacheData.timestamp // 🔥 ВРЕМЯ ИЗ КЭША!
                    };

                    // 💾 КЭШИРУЕМ ДАННЫЕ ПУЛА В binArraysCache
                    // (данные уже сохранены в binArraysCache выше)
                    
                    results.push(poolData);

                } catch (poolError) {
                    const poolStr = this.getPoolStr(poolAddress);
                    console.log(`❌ Ошибка пула ${poolStr.slice(0, 8)}: ${poolError.message}`);
                }
            }

            const duration = Date.now() - startTime;
            
            if (!this.arbitrageMode) {
                console.log(`✅ Данные получены: ${results.length}/${poolAddresses.length} пулов за ${duration}ms`);
            }

            return results;

        } catch (error) {
            console.error(`❌ Ошибка получения данных:`, error.message);
            return [];
        }
    }

    /**
     * 🎯 СОЗДАНИЕ SWAP ТРАНЗАКЦИИ БЕЗ ЛИШНИХ ЗАПРОСОВ!
     * Использует кэшированный DLMM инстанс + getBinArrayForSwap() для создания транзакции
     */
    async createFastSwap(poolAddress, inAmount, swapForY = true, userPublicKey, minOutAmount = null) {
        const startTime = Date.now();
        
        try {
            const poolStr = this.getPoolStr(poolAddress);
            
            // 🎯 ПОЛУЧАЕМ DLMM ИНСТАНС ИЗ КЭША
            const dlmmInstance = this.dlmmInstancesCache.get(poolStr);
            
            if (!dlmmInstance) {
                throw new Error(`DLMM инстанс не найден в кэше для ${poolStr.slice(0, 8)}. Сначала вызовите getArbitrageData()`);
            }

            if (!this.arbitrageMode) {
                console.log(`🎯 СОЗДАНИЕ SWAP БЕЗ ДОПОЛНИТЕЛЬНЫХ RPC: ${poolStr.slice(0, 8)}`);
            }

            // 🔥 СОЗДАЕМ МГНОВЕННЫЙ QUOTE С НАШИМИ СВЕЖИМИ ДАННЫМИ!
            const quote = this.createFastQuoteFromCache(poolStr, inAmount, swapForY);

            const binArrays = quote.binArraysPubkey.map(pubkey => ({ publicKey: pubkey }));
            
            // 🔥 ЕСЛИ НЕ УКАЗАН minOutAmount - СТАВИМ МИНИМУМ (НАМ ПОХУЙ НА ТОЧНЫЙ ВЫХОД!)
            const finalMinOutAmount = minOutAmount || new BN(1);
            
            // 🎯 СОЗДАЕМ ТРАНЗАКЦИЮ НАПРЯМУЮ БЕЗ QUOTE!
            const poolPubkey = new PublicKey(poolStr);
            
            const swapTx = await dlmmInstance.swap({
                inToken: swapForY ? dlmmInstance.lbPair.tokenXMint : dlmmInstance.lbPair.tokenYMint,
                outToken: swapForY ? dlmmInstance.lbPair.tokenYMint : dlmmInstance.lbPair.tokenXMint,
                inAmount: inAmount,
                minOutAmount: finalMinOutAmount, // МИНИМУМ - НАМ ПОХУЙ!
                lbPair: poolPubkey,
                user: userPublicKey,
                binArraysPubkey: binArrays.map(ba => ba.publicKey)
            });

            const duration = Date.now() - startTime;
            
            if (!this.arbitrageMode) {
                console.log(`🎯 Swap транзакция создана за ${duration}ms БЕЗ ДОПОЛНИТЕЛЬНЫХ RPC!`);
                console.log(`   💰 Input: ${inAmount.toString()}`);
                console.log(`   🔗 Инструкций: ${swapTx.instructions.length}`);
            }

            return {
                transaction: swapTx,
                duration
            };

        } catch (error) {
            console.error(`❌ Ошибка создания swap транзакции:`, error.message);
            return null;
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ЦЕНЫ ИЗ КЭША
     */
    getPrice(poolAddress) {
        const poolData = this.binArraysCache.get(this.getPoolStr(poolAddress));
        return poolData ? poolData.activeBinPrice : null;
    }

    /**
     * 🎯 ПРОВЕРКА ГОТОВНОСТИ К SWAP
     */
    isReadyForSwap(poolAddress) {
        const poolData = this.binArraysCache.get(this.getPoolStr(poolAddress));
        return poolData && poolData.readyForSwap;
    }

    /**
     * 🎯 ВКЛЮЧЕНИЕ РЕЖИМА АРБИТРАЖА (скрывает логи)
     */
    startArbitrageMode(sessionId) {
        this.arbitrageMode = true;
        this.arbitrageSessionId = sessionId;
    }

    /**
     * 🎯 ВЫКЛЮЧЕНИЕ РЕЖИМА АРБИТРАЖА
     */
    endArbitrageMode() {
        this.arbitrageMode = false;
        this.arbitrageSessionId = null;
    }

    /**
     * 🎯 ОЧИСТКА УСТАРЕВШЕГО КЭША (ВЫЗЫВАЕТСЯ ВРУЧНУЮ) + ПРОДАКШЕН КЭШИ
     */
    cleanExpiredCache() {
        const now = Date.now();
        let cleaned = 0;

        // 🔥 ОЧИСТКА ОСНОВНОГО КЭША
        for (const [poolAddress, data] of this.binArraysCache.entries()) {
            if (now - data.timestamp > this.ACTIVE_BIN_CACHE_DURATION) {
                this.binArraysCache.delete(poolAddress);
                cleaned++;
            }
        }

        // 🚀 ПРОДАКШЕН: ОЧИСТКА PDA КЭША
        for (const [key, data] of this.pdaCache.entries()) {
            if (now - data.timestamp > this.pdaCacheTimeout) {
                this.pdaCache.delete(key);
                cleaned++;
            }
        }

        // 🚀 ПРОДАКШЕН: ОЧИСТКА КЭША СУЩЕСТВОВАНИЯ АККАУНТОВ
        for (const [key, data] of this.accountExistsCache.entries()) {
            if (now - data.timestamp > this.accountExistsCacheTimeout) {
                this.accountExistsCache.delete(key);
                cleaned++;
            }
        }

        if (cleaned > 0 && !this.arbitrageMode) {
            console.log(`🧹 Очищено ${cleaned} устаревших записей из всех кэшей (основной + PDA + существование)`);
        }
    }

    /**
     * 🎯 МЕТОДЫ СОВМЕСТИМОСТИ СО СТАРЫМ КОДОМ
     */

    // Совместимость с batchUpdateAllActiveBins
    async batchUpdateAllActiveBins(poolAddresses) {
        return await this.getArbitrageData(poolAddresses);
    }

    /**
     * 🔥 ПРАВИЛЬНЫЙ getActiveBinFromCache() - ЧИТАЕТ ИЗ binArraysCache!
     */
    getActiveBinFromCache(poolAddress) {
        const poolStr = typeof poolAddress === 'string' ? poolAddress : poolAddress.toString();

        // 🔥 ЧИТАЕМ ИЗ binArraysCache ГДЕ СОХРАНЯЮТСЯ РЕАЛЬНЫЕ ДАННЫЕ!
        const cacheData = this.binArraysCache.get(poolStr);

        if (cacheData) {
            console.log(`✅ getActiveBinFromCache: найдены данные для ${poolStr.slice(0,8)}`);
            console.log(`   activeBinId: ${cacheData.activeBinId}`);
            console.log(`   activeBin: ${cacheData.activeBin ? 'ЕСТЬ' : 'НЕТ'}`);
            return cacheData;
        }

        console.log(`❌ getActiveBinFromCache: НЕТ данных для ${poolStr.slice(0,8)}`);
        return null;
    }

    // 🔥 ПРАВИЛЬНЫЙ getAllExactPrices ИЗ binArraysCache!
    getAllExactPrices() {
        const prices = new Map();
        const now = Date.now();

        console.log(`🔍 ПРОВЕРКА КЭША: ${this.binArraysCache.size} пулов в кэше`);

        for (const [poolAddress, poolData] of this.binArraysCache.entries()) {
            console.log(`   🔍 Пул ${poolAddress.slice(0,8)}: данные ${poolData ? 'ЕСТЬ' : 'НЕТ'}`);

            if (poolData && poolData.activeBinPrice && poolData.activeBinId !== undefined) {
                const age = now - poolData.timestamp;
                const isFresh = age < this.ACTIVE_BIN_CACHE_DURATION;

                prices.set(poolAddress, {
                    price: poolData.activeBinPrice,
                    binId: poolData.activeBinId,
                    age: age,
                    fresh: isFresh,
                    timestamp: poolData.timestamp
                });

                if (!this.arbitrageMode) {
                    console.log(`      ✅ Цена: $${poolData.activeBinPrice.toFixed(6)}, возраст: ${age}ms, свежий: ${isFresh}`);
                }
            } else {
                if (!this.arbitrageMode) {
                    console.log(`      ❌ Нет данных цены для ${poolAddress.slice(0,8)}`);
                }
            }
        }

        if (!this.arbitrageMode) {
            console.log(`✅ ПОЛУЧЕНО ${prices.size} ЦЕН ИЗ КЭША`);
        }
        return prices;
    }

    // 🔥 ГАРАНТИРОВАННОЕ ПОЛУЧЕНИЕ ДАННЫХ - ВСЕГДА ВОЗВРАЩАЕТ ДАННЫЕ!
    async getGuaranteedPrices() {
        let prices = this.getAllExactPrices();

        // Если данных нет или они устарели - принудительно обновляем
        if (prices.size === 0) {
            console.log('⚠️ НЕТ ДАННЫХ В КЭШЕ - ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ!');

            const FIXED_POOLS = [
                '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
            ];

            // Принудительно обновляем все пулы
            for (const poolAddress of FIXED_POOLS) {
                try {
                    await this.updatePool(poolAddress); // 🔥 ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ updatePool ВМЕСТО updateActiveBin
                } catch (error) {
                    console.log(`❌ Ошибка принудительного обновления ${poolAddress.slice(0,8)}: ${error.message}`);
                }
            }

            // Получаем данные еще раз
            prices = this.getAllExactPrices();
        }

        return prices;
    }

    // 🔥 ГАРАНТИРОВАННОЕ ПОЛУЧЕНИЕ ДАННЫХ - ВСЕГДА ВОЗВРАЩАЕТ ДАННЫЕ!
    async getGuaranteedPrices() {
        let prices = this.getAllExactPrices();

        // Если данных нет или они устарели - принудительно обновляем
        if (prices.size === 0) {
            console.log('⚠️ НЕТ ДАННЫХ В КЭШЕ - ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ!');

            const FIXED_POOLS = [
                '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
            ];

            // Принудительно обновляем все пулы
            for (const poolAddress of FIXED_POOLS) {
                try {
                    await this.updatePool(poolAddress); // 🔥 ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ updatePool ВМЕСТО updateActiveBin
                } catch (error) {
                    console.log(`❌ Ошибка принудительного обновления ${poolAddress.slice(0,8)}: ${error.message}`);
                }
            }

            // Получаем данные еще раз
            prices = this.getAllExactPrices();
        }

        return prices;
    }

    // 🚫 СОВМЕСТИМОСТЬ: getDLMMInstance - возвращает кэшированный DLMM
    async getDLMMInstance(poolAddress) {
        if (!poolAddress) {
            throw new Error(`🚨 КРИТИЧЕСКАЯ ОШИБКА: poolAddress не передан в getDLMMInstance! Получен: ${poolAddress}`);
        }

        const poolStr = this.getPoolStr(poolAddress);
        const dlmmInstance = this.dlmmInstancesCache.get(poolStr);

        if (!dlmmInstance) {
            console.log(`⚠️ DLMM инстанс не найден в кэше для ${poolStr.slice(0, 8)}. Вызовите getArbitrageData() сначала.`);
            return null;
        }

        return dlmmInstance;
    }

    // ❌ УДАЛЕН: startPermanentAutoUpdate() - ЗАМЕНЕН НА СТАБИЛЬНУЮ СИСТЕМУ

    // ❌ УДАЛЕН: updateActiveBin() - ЗАМЕНЕН НА updatePoolStable()

    // 🚫 СОВМЕСТИМОСТЬ: getActiveBinData - получает данные активного бина
    getActiveBinData(poolAddress) {
        const poolStr = this.getPoolStr(poolAddress);
        const poolData = this.binArraysCache.get(poolStr);

        if (!poolData) {
            console.log(`🔍 ОТЛАДКА КЭША: Ищем ${poolAddress}`);
            console.log(`🔍 ОТЛАДКА КЭША: Ищем ${poolStr.slice(0, 8)}`);
            console.log(`❌ НЕТ КЭША АКТИВНЫХ БИНОВ ДЛЯ ${poolStr.slice(0, 8)}!`);
            return null;
        }

        // 🔥 ВОЗВРАЩАЕМ ДАННЫЕ С activeBin ДЛЯ ОПТИМИЗИРОВАННОГО КОДА!
        return {
            activeBinId: poolData.activeBinId,
            activeBinPrice: poolData.activeBinPrice,
            activeBin: poolData.activeBin, // 🔥 ГЛАВНОЕ! ВОЗВРАЩАЕМ activeBin!
            bins: poolData.activeBin ? [poolData.activeBin] : [], // 🔥 ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ bins КАК МАССИВ!
            binArrays: poolData.binArrays || [], // 🔥 ИСПРАВЛЕНИЕ: ЗАЩИТА ОТ undefined!
            timestamp: poolData.timestamp,
            poolAddress: poolData.poolAddress // 🔥 ДОБАВЛЯЕМ АДРЕС ПУЛА ДЛЯ АНАЛИЗАТОРА!
        };
    }

    // 🔥 СОЗДАНИЕ МГНОВЕННОГО DLMM С НАШИМИ СВЕЖИМИ ДАННЫМИ!
    async createFastDLMM(connection, poolPubkey) {
        const poolStr = poolPubkey.toString();
        console.log(`🚀 СОЗДАЕМ МГНОВЕННЫЙ DLMM С НАШИМИ ДАННЫМИ: ${poolStr.slice(0,8)}...`);

        // 🔥 ПОЛУЧАЕМ НАШИ СВЕЖИЕ ДАННЫЕ ИЗ ПРАВИЛЬНОГО КЭША!
        const cacheData = this.binArraysCache.get(poolStr);
        if (!cacheData) {
            throw new Error(`❌ НЕТ СВЕЖИХ ДАННЫХ В binArraysCache ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        // 🔥 СОЗДАЕМ DLMM С НАШИМИ СВЕЖИМИ ДАННЫМИ БЕЗ RPC!
        const provider = new (require('@coral-xyz/anchor')).AnchorProvider(
            connection,
            {},
            require('@coral-xyz/anchor').AnchorProvider.defaultOptions()
        );

        const program = new (require('@coral-xyz/anchor')).Program(
            require('@meteora-ag/dlmm').IDL,
            new PublicKey("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"),
            provider
        );

        // 🔥 СОЗДАЕМ FAKE LB PAIR С НАШИМИ СВЕЖИМИ ДАННЫМИ!
        const fakeLbPair = {
            activeId: cacheData.activeBinId,
            binStep: 25, // Стандартный bin step
            tokenXMint: new PublicKey("So11111111111111111111111111111111111111112"), // SOL
            tokenYMint: new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), // USDC
            reserveX: new PublicKey("11111111111111111111111111111111"),
            reserveY: new PublicKey("11111111111111111111111111111111"),
            rewardInfos: [
                { mint: new PublicKey("11111111111111111111111111111111"), vault: new PublicKey("11111111111111111111111111111111") },
                { mint: new PublicKey("11111111111111111111111111111111"), vault: new PublicKey("11111111111111111111111111111111") }
            ]
        };

        const fakeTokenReserve = {
            publicKey: new PublicKey("11111111111111111111111111111111"),
            mint: { decimals: 9 },
            owner: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"),
            transferHookAccountMetas: []
        };

        const fakeClock = {
            slot: new BN(0),
            unixTimestamp: new BN(Date.now() / 1000),
            epoch: new BN(0)
        };

        // 🔥 СОЗДАЕМ МГНОВЕННЫЙ DLMM!
        const fastDLMM = new DLMM(
            poolPubkey,
            program,
            fakeLbPair,
            null, // binArrayBitmapExtension
            fakeTokenReserve, // tokenX
            fakeTokenReserve, // tokenY
            [null, null], // rewards
            fakeClock
        );

        console.log(`✅ МГНОВЕННЫЙ DLMM СОЗДАН С НАШИМИ ДАННЫМИ: activeBinId=${cacheData.activeBinId}, price=$${cacheData.activeBinPrice.toFixed(4)}`);
        return fastDLMM;
    }

    // 🔥 СОЗДАНИЕ МГНОВЕННОГО QUOTE С НАШИМИ СВЕЖИМИ ДАННЫМИ!
    createFastQuoteFromCache(poolStr, inAmount, swapForY) {
        console.log(`⚡ СОЗДАЕМ МГНОВЕННЫЙ QUOTE С НАШИМИ ДАННЫМИ...`);

        // 🔥 ПОЛУЧАЕМ НАШИ СВЕЖИЕ ДАННЫЕ ИЗ binArraysCache!
        const cacheData = this.binArraysCache.get(poolStr);
        if (!cacheData || !cacheData.threeBins) {
            throw new Error(`❌ НЕТ СВЕЖИХ 3 БИНОВ В binArraysCache ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        // 🔥 РАССЧИТЫВАЕМ ПРИМЕРНЫЙ ВЫХОД ИЗ НАШИХ 3 БИНОВ!
        const activeBinPrice = cacheData.activeBinPrice;
        let estimatedOut;

        if (swapForY) {
            // SOL -> USDC: умножаем на цену
            estimatedOut = new BN(Math.floor(inAmount.toNumber() * activeBinPrice * 0.997)); // -0.3% комиссия
        } else {
            // USDC -> SOL: делим на цену
            estimatedOut = new BN(Math.floor(inAmount.toNumber() / activeBinPrice * 0.997)); // -0.3% комиссия
        }

        // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ BIN ARRAYS ИЗ КЭША!
        if (!cacheData.binArrays || cacheData.binArrays.length === 0) {
            throw new Error(`❌ НЕТ BIN ARRAYS В КЭШЕ ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        const realBinArrays = cacheData.binArrays.map(addr => new PublicKey(addr));
        console.log(`🔥 ИСПОЛЬЗУЕМ ${realBinArrays.length} РЕАЛЬНЫХ BIN ARRAYS ИЗ КЭША!`);

        // 🔥 МГНОВЕННЫЙ QUOTE БЕЗ RPC ЗАПРОСОВ!
        const fastQuote = {
            inAmount: inAmount,
            outAmount: estimatedOut,
            minOutAmount: estimatedOut.mul(new BN(99)).div(new BN(100)), // -1% slippage
            binArraysPubkey: realBinArrays,
            priceImpact: 0.1, // 0.1% примерно
            fee: inAmount.mul(new BN(3)).div(new BN(1000)) // 0.3% комиссия
        };

        console.log(`⚡ МГНОВЕННЫЙ QUOTE СОЗДАН: ${inAmount.toString()} -> ${estimatedOut.toString()}`);
        return fastQuote;
    }
    /**
     * 🚀 ЗАГРУЗКА ВСЕХ СУЩЕСТВУЮЩИХ BIN ARRAYS ПРИ СТАРТЕ
     * Сканирует блокчейн и находит все существующие BinArray для наших пулов
     */
    async loadAllExistingBinArrays() {
        console.log('🚀 ЗАГРУЖАЕМ ВСЕ СУЩЕСТВУЮЩИЕ BIN ARRAYS...');

        try {
            const connection = await this.getRPCForOperation('meteora');

            for (const [poolName, poolAddress] of Object.entries(this.POOLS)) {
                console.log(`🔍 Сканируем ${poolName}: ${poolAddress.toString().slice(0,8)}...`);

                const existingBinArrays = await this.scanExistingBinArrays(poolAddress, connection);

                if (existingBinArrays.length > 0) {
                    console.log(`✅ ${poolName}: найдено ${existingBinArrays.length} существующих BinArray`);

                    // Сохраняем в кэш
                    this.existingBinArraysCache.set(poolAddress.toString(), existingBinArrays);

                    // Выводим первые несколько для диагностики
                    existingBinArrays.slice(0, 3).forEach((binArray, index) => {
                        console.log(`   [${index}] Bin ${binArray.binId} (aligned ${binArray.alignedBinId}): ${binArray.address}`);
                    });
                } else {
                    console.log(`⚠️ ${poolName}: не найдено существующих BinArray`);
                }
            }

            console.log('✅ ЗАГРУЗКА СУЩЕСТВУЮЩИХ BIN ARRAYS ЗАВЕРШЕНА');

        } catch (error) {
            console.log(`❌ ОШИБКА ЗАГРУЗКИ СУЩЕСТВУЮЩИХ BIN ARRAYS: ${error.message}`);
        }
    }

    /**
     * 🔍 СКАНИРОВАНИЕ СУЩЕСТВУЮЩИХ BIN ARRAYS ДЛЯ ПУЛА
     */
    async scanExistingBinArrays(poolAddress, connection) {
        const existingBinArrays = [];

        // 🔥 МИНИМАЛЬНЫЙ НАБОР ДЛЯ БЫСТРОГО СКАНИРОВАНИЯ!
        const testBinIds = [
            // ТОЛЬКО САМЫЕ ВАЖНЫЕ ДИАПАЗОНЫ!
            -4400, -1800, 0, 1300  // 4 ЗАПРОСА ВМЕСТО СОТЕН!
        ];

        console.log(`🔥 БЫСТРОЕ СКАНИРОВАНИЕ: ТОЛЬКО ${testBinIds.length} КЛЮЧЕВЫХ БИНОВ!`);

        const possibleSeeds = ['bin_array']; // ТОЛЬКО ОДИН SEED!
        let requestCount = 0;
        const maxRequests = 10; // МАКСИМУМ 10 ЗАПРОСОВ!

        for (const binId of testBinIds) {
            if (requestCount >= maxRequests) {
                console.log(`⚠️ ДОСТИГНУТ ЛИМИТ ${maxRequests} ЗАПРОСОВ - ПРЕРЫВАЕМ!`);
                break;
            }

            const alignedBinId = binId & ~63; // Выравнивание по 64

            for (const seed of possibleSeeds) {
                try {
                    const offsetBuffer = Buffer.alloc(2);
                    offsetBuffer.writeInt16LE(alignedBinId, 0);

                    const [binArrayPDA] = PublicKey.findProgramAddressSync(
                        [
                            Buffer.from(seed),
                            poolAddress.toBuffer(),
                            offsetBuffer
                        ],
                        this.METEORA_DLMM_PROGRAM
                    );

                    // 🔥 TIMEOUT ДЛЯ RPC ЗАПРОСА!
                    requestCount++;
                    console.log(`📡 RPC запрос ${requestCount}/${maxRequests} для bin ${binId}...`);

                    const timeoutPromise = new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('RPC timeout')), 2000)
                    );

                    const accountInfoPromise = connection.getAccountInfo(binArrayPDA);
                    const accountInfo = await Promise.race([accountInfoPromise, timeoutPromise]);

                    if (accountInfo && accountInfo.owner.equals(this.METEORA_DLMM_PROGRAM)) {
                        existingBinArrays.push({
                            binId: binId,
                            alignedBinId: alignedBinId,
                            seed: seed,
                            address: binArrayPDA.toString(),
                            publicKey: binArrayPDA
                        });
                        console.log(`✅ НАЙДЕН BinArray для bin ${binId}!`);
                        break; // Найден, переходим к следующему binId
                    }

                    // МИНИМАЛЬНАЯ ЗАДЕРЖКА
                    await new Promise(resolve => setTimeout(resolve, 100));

                } catch (error) {
                    if (error.message === 'RPC timeout') {
                        console.log(`⏰ TIMEOUT для bin ${binId} - пропускаем`);
                    }
                    // Игнорируем ошибки PDA генерации
                }
            }
        }

        console.log(`✅ БЫСТРОЕ СКАНИРОВАНИЕ ЗАВЕРШЕНО: найдено ${existingBinArrays.length} BinArray за ${requestCount} запросов`);

        return existingBinArrays;
    }

    /**
     * 🔍 ПОИСК BIN ARRAY ПО BIN ID
     */
    findBinArrayByBinId(poolAddress, targetBinId) {
        const poolStr = poolAddress.toString();
        const existingBinArrays = this.existingBinArraysCache.get(poolStr);

        if (!existingBinArrays) {
            console.log(`❌ НЕТ СУЩЕСТВУЮЩИХ BIN ARRAYS ДЛЯ ПУЛА ${poolStr.slice(0,8)}`);
            return null;
        }

        // Ищем точное совпадение или ближайший aligned
        const targetAligned = targetBinId & ~63;

        const exactMatch = existingBinArrays.find(ba => ba.binId === targetBinId);
        if (exactMatch) {
            return exactMatch;
        }

        const alignedMatch = existingBinArrays.find(ba => ba.alignedBinId === targetAligned);
        if (alignedMatch) {
            return alignedMatch;
        }

        console.log(`🔍 НЕ НАЙДЕН BIN ARRAY ДЛЯ BIN ID ${targetBinId} (aligned ${targetAligned})`);
        return null;
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ СУЩЕСТВУЮЩИХ BIN ARRAYS ДЛЯ ДИАПАЗОНА
     */
    getExistingBinArraysForRange(poolAddress, minBinId, maxBinId) {
        const poolStr = poolAddress.toString();
        const existingBinArrays = this.existingBinArraysCache.get(poolStr);

        if (!existingBinArrays) {
            console.log(`🔍 НЕТ СУЩЕСТВУЮЩИХ BIN ARRAYS ДЛЯ ПУЛА ${poolStr.slice(0,8)}`);
            return [];
        }

        // Находим все BinArray в диапазоне
        const rangeArrays = existingBinArrays.filter(ba => {
            return ba.binId >= minBinId && ba.binId <= maxBinId;
        });

        console.log(`🔍 НАЙДЕНО ${rangeArrays.length} BIN ARRAYS В ДИАПАЗОНЕ ${minBinId}-${maxBinId}`);
        return rangeArrays;
    }

    // 🔍 ПРОВЕРКА СВЕЖЕСТИ КЭША
    isCacheFresh(poolAddress, maxAge = 1000) {
        const poolStr = this.getPoolStr(poolAddress);
        const cacheData = this.binArraysCache.get(poolStr);

        if (!cacheData) {
            return false;
        }

        const age = Date.now() - cacheData.timestamp;
        return age < maxAge;
    }

    // 🔍 ПОЛУЧЕНИЕ СТАТИСТИКИ КЭША
    getCacheStats() {
        const now = Date.now();
        const stats = {
            totalPools: this.binArraysCache.size,
            freshPools: 0,
            stalePools: 0,
            emptyPools: 0,
            details: []
        };

        for (const [poolAddress, data] of this.binArraysCache.entries()) {
            const age = data ? (now - data.timestamp) : Infinity;
            const isFresh = age < this.ACTIVE_BIN_CACHE_DURATION;
            const hasData = data && data.activeBinPrice && data.activeBinId !== undefined;

            if (!hasData) {
                stats.emptyPools++;
            } else if (isFresh) {
                stats.freshPools++;
            } else {
                stats.stalePools++;
            }

            stats.details.push({
                pool: poolAddress.slice(0, 8),
                age: age,
                fresh: isFresh,
                hasData: hasData,
                price: hasData ? data.activeBinPrice : null
            });
        }

        return stats;
    }

    // 🔍 ПРИНУДИТЕЛЬНАЯ ПРОВЕРКА КЭША
    validateCache() {
        console.log('🔍 ВАЛИДАЦИЯ КЭША:');
        const stats = this.getCacheStats();

        console.log(`   📊 Всего пулов: ${stats.totalPools}`);
        console.log(`   ✅ Свежих: ${stats.freshPools}`);
        console.log(`   ⚠️ Устаревших: ${stats.stalePools}`);
        console.log(`   ❌ Пустых: ${stats.emptyPools}`);

        if (stats.emptyPools > 0) {
            console.log('   🚨 ОБНАРУЖЕНЫ ПУСТЫЕ ПУЛЫ В КЭШЕ!');
            stats.details.forEach(detail => {
                if (!detail.hasData) {
                    console.log(`      ❌ ${detail.pool}: НЕТ ДАННЫХ`);
                }
            });
        }

        return stats;
    }

    /**
     * 🔥 АВТООБНОВЛЕНИЕ КЭША КАЖДЫЕ 1000MS
     */
    startAutoUpdate() {
        console.log('🔥 ЗАПУСК АВТООБНОВЛЕНИЯ КЭША КАЖДЫЕ 1000MS');

        // 🔥 НЕ ДЕЛАЕМ ПЕРВОЕ ОБНОВЛЕНИЕ СРАЗУ - ЖДЕМ ГОТОВНОСТИ RPC!
        console.log('⏳ Автообновление запущено, первое обновление через 2 секунды...');

        // Затем каждые 1000ms
        this.autoUpdateInterval = setInterval(() => {
            if (!this.isUpdating) {
                this.updateAllPools();
            }
        }, 1000);
    }

    /**
     * 🚀 ЗАПУСК АВТООБНОВЛЕНИЯ ПОСЛЕ ПОЛНОЙ ИНИЦИАЛИЗАЦИИ
     */
    startAutoUpdateAfterInit() {
        console.log('🚀 ЗАПУСК АВТООБНОВЛЕНИЯ ПОСЛЕ ПОЛНОЙ ИНИЦИАЛИЗАЦИИ RPC');

        // Запускаем автообновление
        this.startAutoUpdate();

        // Делаем первое обновление через небольшую задержку
        setTimeout(() => {
            console.log('🔥 ПЕРВОЕ ОБНОВЛЕНИЕ КЭША ПОСЛЕ ИНИЦИАЛИЗАЦИИ');
            this.updateAllPools();
        }, 2000); // 2 секунды задержки для полной готовности RPC
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ ВСЕХ ПУЛОВ
     */
    async updateAllPools() {
        if (this.isUpdating) return;

        this.isUpdating = true;

        try {
            const promises = this.pools.map(poolAddress => this.updatePool(poolAddress));
            await Promise.all(promises);

            // 🔥 СИНХРОНИЗАЦИЯ: ВЫЗЫВАЕМ АНАЛИЗАТОР СРАЗУ ПОСЛЕ ОБНОВЛЕНИЯ КЭША!
            if (this.onCacheUpdated && typeof this.onCacheUpdated === 'function') {
                console.log('🔥 КЭША ОБНОВЛЕН → ЗАПУСКАЕМ АНАЛИЗАТОР СИНХРОННО!');
                setImmediate(() => {
                    this.onCacheUpdated();
                });
            }
        } catch (error) {
            console.error('❌ ОШИБКА СТАБИЛЬНОГО ОБНОВЛЕНИЯ ПУЛОВ:', error.message);
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ ОДНОГО ПУЛА
     */
    async updatePool(poolAddress) {
        const poolStr = this.getPoolStr(poolAddress); // 🔥 ОБЪЯВЛЯЕМ ВНАЧАЛЕ!

        try {
            const connection = await this.getRPCForOperation('meteora');
            const poolPubkey = new PublicKey(poolAddress);

            // 🔥 СОЗДАЕМ DLMM INSTANCE (КАК В РАБОЧЕМ КОДЕ)
            const dlmmPool = await DLMM.create(connection, poolPubkey);

            // 🔥 ПОЛУЧАЕМ ТОЛЬКО АКТИВНЫЙ БИН (КАК В РАБОЧЕМ КОДЕ)
            const { activeBin, bins } = await dlmmPool.getBinsAroundActiveBin(0, 0);

            if (!bins || bins.length === 0) {
                throw new Error('Не удалось получить активный бин');
            }

            const activeBinData = bins[0];
            const activeBinId = dlmmPool.lbPair.activeId;

            // 🔥 ГЕНЕРИРУЕМ BIN ARRAY PDA (КАК В РАБОЧЕМ КОДЕ)
            const chunkIndex = Math.floor(activeBinId / 64);
            const indexBuffer = Buffer.alloc(8);
            indexBuffer.writeBigInt64LE(BigInt(chunkIndex), 0);

            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from("bin_array"),
                poolPubkey.toBuffer(),
                indexBuffer
            ], this.METEORA_DLMM_PROGRAM);

            // 🔥 ГЕНЕРИРУЕМ BIN LIQUIDITY PDA (КАК В РАБОЧЕМ КОДЕ)
            const binIdBuffer = Buffer.alloc(4);
            binIdBuffer.writeInt32LE(activeBinId, 0);

            const [binLiquidityPDA] = PublicKey.findProgramAddressSync([
                Buffer.from("bin"),
                poolPubkey.toBuffer(),
                binIdBuffer
            ], this.METEORA_DLMM_PROGRAM);

            // 🔥 СОХРАНЯЕМ В КЭШ (СОВМЕСТИМО СО СТАРЫМ ФОРМАТОМ)
            const cacheData = {
                poolAddress: poolStr, // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ КЛЮЧ!
                activeBinId,
                activeBinPrice: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                activeBin: {
                    binId: activeBinId,
                    price: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                    pricePerToken: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                    isActive: true,
                    xAmount: activeBinData.xAmount || 0,
                    yAmount: activeBinData.yAmount || 0,
                    supply: activeBinData.supply || 0,
                    liquidityX: activeBinData.xAmount || 0,
                    liquidityY: activeBinData.yAmount || 0
                },
                binArrays: [binArrayPDA.toString()], // Массив с одним BinArray
                binLiquidityPDA: binLiquidityPDA.toString(),
                chunkIndex,
                timestamp: Date.now(),
                dlmmPool // Сохраняем DLMM instance
            };

            this.binArraysCache.set(poolStr, cacheData);

            // 🔥 КРИТИЧЕСКИ ВАЖНО: СОХРАНЯЕМ DLMM INSTANCE В ОТДЕЛЬНЫЙ КЭШ!
            this.dlmmInstancesCache.set(poolStr, dlmmPool);

            console.log(`✅ ${poolStr.slice(0,8)}: активный бин ${activeBinId}, цена ${cacheData.activeBin.price}`);

        } catch (error) {
            console.error(`❌ ОШИБКА СТАБИЛЬНОГО ОБНОВЛЕНИЯ ${poolStr.slice(0,8)}:`, error.message);
        }
    }

    /**
     * 🔥 ОСТАНОВКА АВТООБНОВЛЕНИЯ
     */
    stopAutoUpdate() {
        if (this.autoUpdateInterval) {
            clearInterval(this.autoUpdateInterval);
            this.autoUpdateInterval = null;
            console.log('🔥 АВТООБНОВЛЕНИЕ ОСТАНОВЛЕНО');
        }
    }
}

module.exports = MeteoraBinCacheManager;
