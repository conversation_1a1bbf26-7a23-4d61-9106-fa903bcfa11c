/**
 * 🔥 METEORA AGGRESSIVE SEARCH
 * АГРЕССИВНЫЙ ПОИСК ОСТАВШИХСЯ PDA С ИСПОЛЬЗОВАНИЕМ ИЗВЕСТНЫХ ПАТТЕРНОВ
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');

// === CONFIGURATION ===
const CONFIG = {
  METEORA_PROGRAM_ID: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
  
  // Ваши целевые PDA
  TARGET_PDAS: {
    position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
    bitmapExtension: '4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH'
  },
  
  // Ваши параметры
  LB_PAIR: new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'),
  USER: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'),
  
  // Найденная формула для bin_array
  FOUND_BIN_ARRAY_INDEX: -71
};

// === AGGRESSIVE SEARCHER ===
class AggressiveSearcher {
  
  /**
   * 🔥 АГРЕССИВНЫЙ ПОИСК POSITION PDA
   */
  static searchPositionAggressively() {
    console.log('\n🔥 АГРЕССИВНЫЙ ПОИСК POSITION PDA:');
    console.log(`Target: ${CONFIG.TARGET_PDAS.position}`);
    
    // Расширенный список префиксов из анализа других проектов
    const prefixes = [
      // Стандартные
      'position', 'Position', 'POSITION',
      // Вариации
      'user_position', 'userPosition', 'UserPosition',
      'lb_position', 'lbPosition', 'LbPosition',
      'liquidity_position', 'liquidityPosition', 'LiquidityPosition',
      // Короткие
      'pos', 'Pos', 'POS', 'user_pos', 'userPos',
      // Meteora специфичные
      'dlmm_position', 'dlmmPosition', 'DlmmPosition',
      'meteora_position', 'meteoraPosition', 'MeteoraPosition',
      // Общие
      'user', 'User', 'USER', 'account', 'Account', 'ACCOUNT',
      'user_account', 'userAccount', 'UserAccount',
      // Экзотические
      'lp', 'LP', 'liquidity', 'Liquidity', 'LIQUIDITY',
      'stake', 'Stake', 'STAKE', 'deposit', 'Deposit'
    ];
    
    const lbPair = CONFIG.LB_PAIR;
    const user = CONFIG.USER;
    
    // Все возможные порядки seeds
    const seedOrders = [
      // Стандартные порядки
      (p, lp, u, i) => [p, lp, u, i],
      (p, lp, u, i) => [p, u, lp, i],
      (p, lp, u, i) => [p, lp, u],
      (p, lp, u, i) => [p, u, lp],
      // С индексом в начале
      (p, lp, u, i) => [p, i, lp, u],
      (p, lp, u, i) => [p, i, u, lp],
      // Без префикса
      (p, lp, u, i) => [lp, u, i],
      (p, lp, u, i) => [u, lp, i],
      // Обратные порядки
      (p, lp, u, i) => [i, u, lp, p],
      (p, lp, u, i) => [i, lp, u, p],
      // Только пара параметров
      (p, lp, u, i) => [p, u],
      (p, lp, u, i) => [p, lp],
      (p, lp, u, i) => [u, lp]
    ];
    
    for (const prefix of prefixes) {
      console.log(`  Тестируем prefix: "${prefix}"`);
      
      for (let orderIndex = 0; orderIndex < seedOrders.length; orderIndex++) {
        for (let positionIndex = 0; positionIndex < 1000; positionIndex++) {
          
          // Все возможные кодирования индекса
          const indexEncodings = [
            this.encodeU8(positionIndex),
            this.encodeU16(positionIndex),
            this.encodeI16(positionIndex),
            this.encodeU32(positionIndex),
            this.encodeI32(positionIndex),
            this.encodeU64(positionIndex),
            this.encodeI64(positionIndex),
            // Специальные кодирования
            this.encodeBE16(positionIndex),
            this.encodeBE32(positionIndex),
            // Как строка
            Buffer.from(positionIndex.toString()),
            // Как hex строка
            Buffer.from(positionIndex.toString(16).padStart(2, '0')),
            // Пустой буфер для случаев без индекса
            null
          ].filter(Boolean);
          
          for (const indexEncoding of indexEncodings) {
            try {
              const seeds = seedOrders[orderIndex](
                Buffer.from(prefix),
                lbPair.toBuffer(),
                user.toBuffer(),
                indexEncoding
              ).filter(Boolean);
              
              if (seeds.length === 0) continue;
              
              const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.METEORA_PROGRAM_ID);
              
              if (pda.toBase58() === CONFIG.TARGET_PDAS.position) {
                console.log(`🎯 НАЙДЕН POSITION PDA!`);
                console.log(`  Prefix: "${prefix}"`);
                console.log(`  Order: ${orderIndex}`);
                console.log(`  Position Index: ${positionIndex}`);
                console.log(`  Index Encoding: ${indexEncoding ? indexEncoding.toString('hex') : 'none'}`);
                console.log(`  Bump: ${bump}`);
                console.log(`  Seeds:`);
                seeds.forEach((seed, i) => {
                  console.log(`    [${i}] ${this.decodeSeed(seed)}`);
                });
                
                return {
                  prefix,
                  orderIndex,
                  positionIndex,
                  indexEncoding: indexEncoding ? indexEncoding.toString('hex') : null,
                  bump,
                  seeds: seeds.map(s => s.toString('hex'))
                };
              }
            } catch (e) {
              // Игнорируем ошибки
            }
          }
        }
      }
    }
    
    console.log('❌ Position PDA не найден в агрессивном поиске');
    return null;
  }

  /**
   * 🗺️ АГРЕССИВНЫЙ ПОИСК BITMAP EXTENSION PDA
   */
  static searchBitmapExtensionAggressively() {
    console.log('\n🗺️ АГРЕССИВНЫЙ ПОИСК BITMAP EXTENSION PDA:');
    console.log(`Target: ${CONFIG.TARGET_PDAS.bitmapExtension}`);
    
    // Расширенный список префиксов
    const prefixes = [
      // Стандартные
      'bitmap_extension', 'bitmapExtension', 'BitmapExtension', 'BITMAP_EXTENSION',
      'bin_array_bitmap_extension', 'binArrayBitmapExtension',
      // Короткие
      'bitmap', 'Bitmap', 'BITMAP',
      'bin_bitmap', 'binBitmap', 'BinBitmap',
      'array_bitmap', 'arrayBitmap', 'ArrayBitmap',
      // Вариации
      'extension', 'Extension', 'EXTENSION',
      'bin_extension', 'binExtension', 'BinExtension',
      'array_extension', 'arrayExtension', 'ArrayExtension',
      // Meteora специфичные
      'dlmm_bitmap', 'dlmmBitmap', 'DlmmBitmap',
      'meteora_bitmap', 'meteoraBitmap', 'MeteoraB itmap',
      // Экзотические
      'bits', 'Bits', 'BITS', 'mask', 'Mask', 'MASK'
    ];
    
    const lbPair = CONFIG.LB_PAIR;
    
    // Используем найденный bin_array index для вычисления возможных binId
    const baseBinIds = [];
    
    // Добавляем значения на основе найденного bin_array index -71
    for (let multiplier = 1; multiplier <= 1000; multiplier++) {
      baseBinIds.push(CONFIG.FOUND_BIN_ARRAY_INDEX * multiplier);
      baseBinIds.push(-CONFIG.FOUND_BIN_ARRAY_INDEX * multiplier);
    }
    
    // Добавляем стандартные значения
    for (let i = -20000; i <= 20000; i += 32) {
      baseBinIds.push(i);
    }
    
    for (const prefix of prefixes) {
      console.log(`  Тестируем prefix: "${prefix}"`);
      
      for (const baseBinId of baseBinIds) {
        // Разные выравнивания
        const alignments = [1, 2, 4, 8, 16, 32, 64, 70, 128, 256, 512, 1024];
        
        for (const alignment of alignments) {
          const alignedBinId = Math.floor(baseBinId / alignment) * alignment;
          
          // Все возможные кодирования
          const encodings = [
            { type: 'i16', buffer: this.encodeI16(alignedBinId) },
            { type: 'u16', buffer: this.encodeU16(alignedBinId) },
            { type: 'i32', buffer: this.encodeI32(alignedBinId) },
            { type: 'u32', buffer: this.encodeU32(alignedBinId) },
            { type: 'i64', buffer: this.encodeI64(alignedBinId) },
            { type: 'u64', buffer: this.encodeU64(alignedBinId) },
            // Big endian варианты
            { type: 'i16_be', buffer: this.encodeBE16(alignedBinId) },
            { type: 'i32_be', buffer: this.encodeBE32(alignedBinId) },
            // Как строка
            { type: 'string', buffer: Buffer.from(alignedBinId.toString()) }
          ].filter(enc => enc.buffer !== null);
          
          for (const encoding of encodings) {
            // Разные порядки seeds
            const seedVariants = [
              [Buffer.from(prefix), lbPair.toBuffer(), encoding.buffer],
              [Buffer.from(prefix), encoding.buffer, lbPair.toBuffer()],
              [lbPair.toBuffer(), Buffer.from(prefix), encoding.buffer],
              [encoding.buffer, Buffer.from(prefix), lbPair.toBuffer()]
            ];
            
            for (const seeds of seedVariants) {
              try {
                const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.METEORA_PROGRAM_ID);
                
                if (pda.toBase58() === CONFIG.TARGET_PDAS.bitmapExtension) {
                  console.log(`🎯 НАЙДЕН BITMAP EXTENSION PDA!`);
                  console.log(`  Prefix: "${prefix}"`);
                  console.log(`  Base Bin ID: ${baseBinId}`);
                  console.log(`  Alignment: ${alignment}`);
                  console.log(`  Aligned Bin ID: ${alignedBinId}`);
                  console.log(`  Encoding: ${encoding.type}`);
                  console.log(`  Bump: ${bump}`);
                  console.log(`  Seeds:`);
                  seeds.forEach((seed, i) => {
                    console.log(`    [${i}] ${this.decodeSeed(seed)}`);
                  });
                  
                  return {
                    prefix,
                    baseBinId,
                    alignment,
                    alignedBinId,
                    encoding: encoding.type,
                    bump,
                    seeds: seeds.map(s => s.toString('hex'))
                  };
                }
              } catch (e) {
                // Игнорируем ошибки
              }
            }
          }
        }
      }
    }
    
    console.log('❌ Bitmap Extension PDA не найден в агрессивном поиске');
    return null;
  }

  // === UTILITY METHODS ===
  static encodeU8(value) {
    if (value < 0 || value > 255) return null;
    return Buffer.from([value]);
  }
  
  static encodeU16(value) {
    if (value < 0 || value > 65535) return null;
    const buf = Buffer.alloc(2);
    buf.writeUInt16LE(value, 0);
    return buf;
  }
  
  static encodeI16(value) {
    if (value < -32768 || value > 32767) return null;
    const buf = Buffer.alloc(2);
    buf.writeInt16LE(value, 0);
    return buf;
  }
  
  static encodeU32(value) {
    if (value < 0 || value > 4294967295) return null;
    const buf = Buffer.alloc(4);
    buf.writeUInt32LE(value, 0);
    return buf;
  }
  
  static encodeI32(value) {
    if (value < -2147483648 || value > 2147483647) return null;
    const buf = Buffer.alloc(4);
    buf.writeInt32LE(value, 0);
    return buf;
  }
  
  static encodeU64(value) {
    if (value < 0) return null;
    try {
      const buf = Buffer.alloc(8);
      buf.writeBigUInt64LE(BigInt(value), 0);
      return buf;
    } catch {
      return null;
    }
  }
  
  static encodeI64(value) {
    try {
      const buf = Buffer.alloc(8);
      buf.writeBigInt64LE(BigInt(value), 0);
      return buf;
    } catch {
      return null;
    }
  }
  
  static encodeBE16(value) {
    if (value < -32768 || value > 32767) return null;
    const buf = Buffer.alloc(2);
    buf.writeInt16BE(value, 0);
    return buf;
  }
  
  static encodeBE32(value) {
    if (value < -2147483648 || value > 2147483647) return null;
    const buf = Buffer.alloc(4);
    buf.writeInt32BE(value, 0);
    return buf;
  }
  
  static decodeSeed(seed) {
    try {
      const str = seed.toString('utf8');
      if (/^[\x20-\x7E]+$/.test(str)) {
        return `"${str}"`;
      }
    } catch {}
    
    if (seed.length === 1) {
      return `u8(${seed[0]})`;
    } else if (seed.length === 2) {
      const num = seed.readInt16LE(0);
      return `i16(${num})`;
    } else if (seed.length === 4) {
      const num = seed.readInt32LE(0);
      return `i32(${num})`;
    } else if (seed.length === 8) {
      const num = seed.readBigInt64LE(0);
      return `i64(${num})`;
    } else if (seed.length === 32) {
      return `PublicKey(${new PublicKey(seed).toBase58()})`;
    }
    
    return `${seed.length}bytes(${seed.toString('hex')})`;
  }
}

// === MAIN EXECUTION ===
async function main() {
  console.log('🔥 METEORA AGGRESSIVE SEARCH');
  console.log('АГРЕССИВНЫЙ ПОИСК ОСТАВШИХСЯ PDA');
  console.log('='.repeat(60));
  
  try {
    const positionResult = AggressiveSearcher.searchPositionAggressively();
    const bitmapResult = AggressiveSearcher.searchBitmapExtensionAggressively();
    
    console.log('\n📊 РЕЗУЛЬТАТЫ АГРЕССИВНОГО ПОИСКА:');
    console.log('='.repeat(60));
    console.log(`Position PDA:        ${positionResult ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
    console.log(`Bitmap Extension:    ${bitmapResult ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
    
    if (positionResult || bitmapResult) {
      console.log('\n🎯 НАЙДЕННЫЕ ФОРМУЛЫ:');
      if (positionResult) {
        console.log('\nPOSITION:', JSON.stringify(positionResult, null, 2));
      }
      if (bitmapResult) {
        console.log('\nBITMAP EXTENSION:', JSON.stringify(bitmapResult, null, 2));
      }
    }
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  AggressiveSearcher,
  CONFIG
};
