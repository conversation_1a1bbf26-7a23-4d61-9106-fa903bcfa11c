# 🎯 Meteora DLMM PDA Interceptor & Reproducer

Полный инструмент для перехвата и воспроизведения процесса создания PDA от Meteora SDK.

## 🚀 Быстрый старт

### Установка зависимостей
```bash
npm install @solana/web3.js bn.js
```

### Основное использование
```bash
# Полный анализ всех PDA
node meteora-pda-interceptor.js

# Быстрые тесты
node quick-pda-test.js test
node quick-pda-test.js positions
node quick-pda-test.js bruteforce
```

## 📋 Что делает инструмент

### 1. Воспроизводит SDK логику
- **Position PDA**: `["position", lbPair, user, positionIndex]`
- **Bitmap Extension PDA**: `["bitmap_extension", lbPair, alignedBinId]`
- **Reserve PDA**: `["reserve", lbPair, tokenMint]`
- **LB Pair PDA**: `["lb_pair", tokenX, tokenY]`
- **Bin Array PDA**: `["bin_array", lbPair, binArrayIndex]`
- **Event Authority PDA**: `["__event_authority"]`

### 2. RPC верификация
- Проверяет существование PDA на блокчейне
- Верифицирует владельца (program ID)
- Показывает размер данных и lamports

### 3. Сканирование существующих PDA
- Перебирает индексы позиций (0-100)
- Ищет bitmap extensions в диапазоне binId
- Находит реально используемые PDA

### 4. Детальный анализ seeds
- Декодирует seeds в читаемый формат
- Показывает hex представление
- Анализирует структуру данных

## 🔧 Конфигурация

Отредактируй `CONFIG` в `meteora-pda-interceptor.js`:

```javascript
const CONFIG = {
  PROGRAM_ID: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
  LB_PAIR: new PublicKey('YOUR_LB_PAIR_HERE'),
  USER: new PublicKey('YOUR_USER_PUBKEY_HERE'),
  TOKEN_X: new PublicKey('So11111111111111111111111111111111111111112'), // WSOL
  TOKEN_Y: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), // USDC
  
  BIN_ID_RANGE: { min: -1024, max: 1024 },
  POSITION_INDEX_RANGE: { min: 0, max: 100 }
};
```

## 📊 Примеры использования

### Найти Position PDA для пользователя
```javascript
const result = await MeteoraSDKInterceptor.getPositionPda(lbPair, user, 0);
console.log('Position PDA:', result.pda.toBase58());
console.log('Seeds:', result.seeds);
```

### Проверить существование PDA
```javascript
const verification = await RPCVerifier.verifyPDAOwnership(pda, programId);
console.log('Exists:', verification.exists);
console.log('Correct owner:', verification.correctOwner);
```

### Найти все позиции пользователя
```javascript
const positions = await PDAScanner.scanPositionPDAs(lbPair, user, 20);
positions.forEach(pos => {
  console.log(`Position[${pos.params.positionIndex}]: ${pos.pda.toBase58()}`);
});
```

## 🧪 Быстрые тесты

### Тест конкретных PDA
```bash
node quick-pda-test.js test
```

### Поиск существующих позиций
```bash
node quick-pda-test.js positions
```

### Брутфорс binId для известного PDA
```bash
node quick-pda-test.js bruteforce
```

### Проверка известного PDA
```bash
node quick-pda-test.js known
```

## 📁 Структура выходных файлов

Результаты сохраняются в `output/meteora-pda-analysis.json`:

```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "config": {
    "programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo",
    "lbPair": "E9kQAzGFvgC7xtgNGCe63meEKBFw2T5yNjzgbJAtNcSJ",
    "user": "bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV"
  },
  "results": [
    {
      "type": "Position",
      "pda": "Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC",
      "bump": 254,
      "seedsHex": ["706f736974696f6e", "..."],
      "seedsDecoded": [
        {
          "index": 0,
          "hex": "706f736974696f6e",
          "decoded": "position",
          "length": 8
        }
      ],
      "verification": {
        "exists": true,
        "correctOwner": true,
        "dataLength": 312,
        "lamports": 2039280
      }
    }
  ]
}
```

## 🔍 Отладка

### Проблемы с seeds
1. Проверь порядок seeds в массиве
2. Убедись в правильном кодировании binId (signed i16 LE)
3. Проверь alignment для bitmap (кратно 256)

### Проблемы с RPC
1. Проверь подключение к RPC
2. Убедись что используешь правильный cluster (mainnet/devnet)
3. Проверь rate limits

### Account discriminator errors
1. PDA сгенерирован неправильно
2. Неправильные seeds или их порядок
3. Неправильный program ID

## 🛠️ Расширение

### Добавить новый тип PDA
```javascript
static async getCustomPda(param1, param2) {
  const seeds = [
    SeedEncoder.encodeString('custom_prefix'),
    param1.toBuffer(),
    SeedEncoder.encodeU64(param2)
  ];
  
  const [pda, bump] = await PublicKey.findProgramAddress(seeds, CONFIG.PROGRAM_ID);
  return { type: 'Custom', pda, bump, seeds: seeds.map(s => s.toString('hex')) };
}
```

### Добавить новый сканер
```javascript
static async scanCustomPDAs(param, range) {
  const results = [];
  for (let i = range.min; i <= range.max; i++) {
    const result = await MeteoraSDKInterceptor.getCustomPda(param, i);
    const verification = await RPCVerifier.verifyPDAOwnership(result.pda, CONFIG.PROGRAM_ID);
    if (verification.exists) {
      results.push({ ...result, verification });
    }
  }
  return results;
}
```

## 📚 Полезные ссылки

- [Meteora DLMM Program](https://solscan.io/account/LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo)
- [Solana PDA Documentation](https://docs.solana.com/developing/programming-model/calling-between-programs#program-derived-addresses)
- [Meteora SDK GitHub](https://github.com/meteora-ag/dlmm-sdk)

## ⚠️ Важные замечания

1. **Bitmap alignment**: binId для bitmap extension должен быть кратен 256
2. **Position index**: начинается с 0, инкрементируется для каждой новой позиции
3. **Encoding**: используй правильное кодирование для числовых значений (LE)
4. **Rate limits**: не делай слишком много RPC запросов одновременно
