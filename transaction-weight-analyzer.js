/**
 * 🔍 АНАЛИЗАТОР ВЕСА ТРАНЗАКЦИЙ METEORA
 * Измеряет размер каждого компонента транзакции
 */

const { Connection, PublicKey, TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
const fs = require('fs');

class TransactionWeightAnalyzer {
    constructor() {
        this.SOLANA_TRANSACTION_LIMIT = 1232; // bytes
        this.results = [];
    }

    /**
     * 🔧 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ
     */
    analyzeTransactionWeight(transaction, description = 'Unknown') {
        console.log(`\n🔍 АНАЛИЗ ВЕСА ТРАНЗАКЦИИ: ${description}`);
        console.log('=' .repeat(80));

        try {
            let serialized;
            let totalSize;

            // Определяем тип транзакции
            if (transaction instanceof VersionedTransaction) {
                serialized = transaction.serialize();
                totalSize = serialized.length;
                console.log(`📊 Тип: VersionedTransaction`);
            } else if (typeof transaction === 'string') {
                // Base64 строка
                serialized = Buffer.from(transaction, 'base64');
                totalSize = serialized.length;
                console.log(`📊 Тип: Base64 String`);
            } else {
                throw new Error('Неподдерживаемый тип транзакции');
            }

            console.log(`📊 Общий размер: ${totalSize} bytes`);
            console.log(`📊 Лимит Solana: ${this.SOLANA_TRANSACTION_LIMIT} bytes`);
            console.log(`📊 Свободно: ${this.SOLANA_TRANSACTION_LIMIT - totalSize} bytes`);
            console.log(`📊 Использовано: ${((totalSize / this.SOLANA_TRANSACTION_LIMIT) * 100).toFixed(1)}%`);

            // Анализ компонентов
            const breakdown = this.analyzeTransactionComponents(transaction);

            // Сохраняем результат
            const result = {
                description,
                totalSize,
                limit: this.SOLANA_TRANSACTION_LIMIT,
                available: this.SOLANA_TRANSACTION_LIMIT - totalSize,
                usagePercent: ((totalSize / this.SOLANA_TRANSACTION_LIMIT) * 100),
                breakdown,
                timestamp: new Date().toISOString()
            };

            this.results.push(result);
            return result;

        } catch (error) {
            console.error(`❌ Ошибка анализа транзакции: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔧 АНАЛИЗ КОМПОНЕНТОВ ТРАНЗАКЦИИ
     */
    analyzeTransactionComponents(transaction) {
        console.log(`\n🔧 ДЕТАЛЬНЫЙ АНАЛИЗ КОМПОНЕНТОВ:`);

        const breakdown = {};

        try {
            if (transaction instanceof VersionedTransaction) {
                const message = transaction.message;

                // 1. Подписи
                const signaturesSize = transaction.signatures.length * 64;
                breakdown.signatures = {
                    count: transaction.signatures.length,
                    size: signaturesSize,
                    description: `${transaction.signatures.length} подписей × 64 bytes`
                };
                console.log(`   📝 Подписи: ${signaturesSize} bytes (${transaction.signatures.length} шт.)`);

                // 2. Message Header
                const headerSize = 3; // version(1) + header(3)
                breakdown.header = {
                    size: headerSize,
                    description: 'Version + Message Header'
                };
                console.log(`   📋 Header: ${headerSize} bytes`);

                // 3. Account Keys
                let accountKeysSize = 0;
                if (message.staticAccountKeys) {
                    accountKeysSize += message.staticAccountKeys.length * 32;
                    breakdown.staticAccounts = {
                        count: message.staticAccountKeys.length,
                        size: message.staticAccountKeys.length * 32,
                        description: `${message.staticAccountKeys.length} статических аккаунтов × 32 bytes`
                    };
                    console.log(`   🔑 Статические аккаунты: ${message.staticAccountKeys.length * 32} bytes (${message.staticAccountKeys.length} шт.)`);
                }

                // 4. Address Lookup Tables
                if (message.addressTableLookups && message.addressTableLookups.length > 0) {
                    let altSize = 0;
                    message.addressTableLookups.forEach((alt, i) => {
                        const tableSize = 32 + // table address
                                         1 + alt.writableIndexes.length + // writable indexes
                                         1 + alt.readonlyIndexes.length;  // readonly indexes
                        altSize += tableSize;
                        console.log(`   📊 ALT ${i + 1}: ${tableSize} bytes (W:${alt.writableIndexes.length}, R:${alt.readonlyIndexes.length})`);
                    });
                    breakdown.addressLookupTables = {
                        count: message.addressTableLookups.length,
                        size: altSize,
                        description: `${message.addressTableLookups.length} ALT таблиц`
                    };
                    accountKeysSize += altSize;
                }

                // 5. Recent Blockhash
                const blockhashSize = 32;
                breakdown.recentBlockhash = {
                    size: blockhashSize,
                    description: 'Recent Blockhash'
                };
                console.log(`   🔗 Recent Blockhash: ${blockhashSize} bytes`);

                // 6. Instructions
                let instructionsSize = 0;
                if (message.compiledInstructions) {
                    message.compiledInstructions.forEach((ix, i) => {
                        const ixSize = 1 + // program id index
                                      1 + ix.accountKeyIndexes.length + // account indexes
                                      this.getCompactArraySize(ix.data.length) + ix.data.length; // data
                        instructionsSize += ixSize;
                        console.log(`   🔧 Instruction ${i + 1}: ${ixSize} bytes (accounts: ${ix.accountKeyIndexes.length}, data: ${ix.data.length})`);
                    });
                }
                breakdown.instructions = {
                    count: message.compiledInstructions ? message.compiledInstructions.length : 0,
                    size: instructionsSize,
                    description: `${message.compiledInstructions ? message.compiledInstructions.length : 0} инструкций`
                };

                breakdown.totalAccountKeys = {
                    size: accountKeysSize,
                    description: 'Все аккаунты (статические + ALT)'
                };

            } else {
                console.log(`   ⚠️ Детальный анализ доступен только для VersionedTransaction`);
            }

        } catch (error) {
            console.error(`❌ Ошибка анализа компонентов: ${error.message}`);
        }

        return breakdown;
    }

    /**
     * 🔧 РАЗМЕР COMPACT ARRAY ENCODING
     */
    getCompactArraySize(length) {
        if (length < 0x80) return 1;
        if (length < 0x4000) return 2;
        if (length < 0x200000) return 3;
        return 4;
    }

    /**
     * 📊 АНАЛИЗ ПОТЕНЦИАЛА СЖАТИЯ BinLiquidityPDA
     */
    analyzeBinLiquidityPDACompression() {
        console.log(`\n🔍 АНАЛИЗ ПОТЕНЦИАЛА СЖАТИЯ BinLiquidityPDA:`);
        console.log('=' .repeat(80));

        // Наши бины: -4561, -4560, -4559
        const ourBins = [-4561, -4560, -4559];
        
        console.log(`📊 Наши бины: ${ourBins.join(', ')}`);
        
        // Chunk calculation
        const chunks = ourBins.map(binId => Math.floor(binId / 64));
        const uniqueChunks = [...new Set(chunks)];
        
        console.log(`📊 Chunks: ${chunks.join(', ')}`);
        console.log(`📊 Уникальные chunks: ${uniqueChunks.join(', ')}`);
        console.log(`📊 Количество уникальных chunks: ${uniqueChunks.length}`);

        // Размер без сжатия
        const binLiquidityPDASize = ourBins.length * 32; // 3 × 32 = 96 bytes
        console.log(`📊 Размер BinLiquidityPDA без сжатия: ${binLiquidityPDASize} bytes`);

        // Размер с ALT сжатием
        const altCompressedSize = ourBins.length * 1; // 3 × 1 = 3 bytes (индексы в ALT)
        console.log(`📊 Размер BinLiquidityPDA с ALT: ${altCompressedSize} bytes`);

        // Экономия
        const savings = binLiquidityPDASize - altCompressedSize;
        console.log(`💾 Экономия от сжатия: ${savings} bytes`);

        // Проблема с диапазоном
        console.log(`\n🚨 ПРОБЛЕМА С ДИАПАЗОНОМ:`);
        console.log(`   BinLiquidityPDA генерируется для КАЖДОГО бина отдельно`);
        console.log(`   Нельзя создать один PDA для диапазона бинов`);
        console.log(`   Каждый бин имеет свой уникальный PDA`);
        console.log(`   Формула: ["bin", lbPair, binId(i32)]`);

        return {
            bins: ourBins,
            chunks: uniqueChunks,
            uncompressedSize: binLiquidityPDASize,
            compressedSize: altCompressedSize,
            savings: savings,
            canCompress: true,
            compressionRatio: ((savings / binLiquidityPDASize) * 100).toFixed(1)
        };
    }

    /**
     * 📊 СОХРАНЕНИЕ ОТЧЕТА
     */
    saveReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalAnalyzed: this.results.length,
                averageSize: this.results.reduce((sum, r) => sum + r.totalSize, 0) / this.results.length,
                maxSize: Math.max(...this.results.map(r => r.totalSize)),
                minSize: Math.min(...this.results.map(r => r.totalSize))
            },
            transactions: this.results,
            compressionAnalysis: this.analyzeBinLiquidityPDACompression()
        };

        fs.writeFileSync('transaction-weight-report.json', JSON.stringify(report, null, 2));
        console.log(`\n✅ Отчет сохранен в: transaction-weight-report.json`);
        
        return report;
    }
}

// Экспорт
module.exports = TransactionWeightAnalyzer;

// Запуск если вызван напрямую
if (require.main === module) {
    const analyzer = new TransactionWeightAnalyzer();
    
    console.log('🔍 АНАЛИЗАТОР ВЕСА ТРАНЗАКЦИЙ METEORA');
    console.log('Для анализа конкретной транзакции используйте:');
    console.log('const analyzer = new TransactionWeightAnalyzer();');
    console.log('analyzer.analyzeTransactionWeight(transaction, "Description");');
    
    // Анализ потенциала сжатия
    analyzer.analyzeBinLiquidityPDACompression();
}
