/**
 * 🔥 METEORA АКТИВНЫЙ БИН БОТ
 * Упрощенная архитектура только для активного бина
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const ActiveBinCacheSimple = require('./active-bin-cache-simple');
const { MeteoraActiveBinInstructions, ActiveBinAnalyzer } = require('./meteora-active-bin-instructions');
const dotenv = require('dotenv');

// 🔥 ЗАГРУЖАЕМ ВСЕ КЛЮЧИ ИЗ .env.solana
dotenv.config({ path: '.env.solana' });

class MeteoraActiveBinBot {
    constructor() {
        // 🔥 ИНИЦИАЛИЗАЦИЯ С КЛЮЧАМИ ИЗ .env.solana
        this.connection = new Connection(
            process.env.HELIUS_RPC_URL ||
            process.env.SYNDICA_RPC_URL ||
            process.env.SOLANA_RPC_URL
        );
        this.activeBinCache = new ActiveBinCacheSimple(this.connection);
        this.instructions = new MeteoraActiveBinInstructions(this.activeBinCache, this.loadRpcConfig());
        this.analyzer = new ActiveBinAnalyzer(this.activeBinCache);
        
        // 🔥 ПУЛЫ
        this.POOL_1 = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6';
        this.POOL_2 = 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y';
        
        // 🔥 СТАТИСТИКА
        this.stats = {
            cycles: 0,
            profit: 0,
            startTime: Date.now()
        };
        
        console.log('🔥 METEORA АКТИВНЫЙ БИН БОТ ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔥 ЗАПУСК БОТА
     */
    async start() {
        console.log('🚀 ЗАПУСК METEORA АКТИВНЫЙ БИН БОТ');
        
        try {
            // 1. Запускаем автоматическое обновление кэша
            this.activeBinCache.startAutoUpdate();
            
            // 2. Ждем первого обновления
            await this.waitForCacheReady();
            
            // 3. Запускаем основной цикл
            this.startMainLoop();
            
        } catch (error) {
            console.error('❌ ОШИБКА ЗАПУСКА БОТА:', error.message);
        }
    }

    /**
     * 🔥 ОЖИДАНИЕ ГОТОВНОСТИ КЭША
     */
    async waitForCacheReady() {
        console.log('⏳ ОЖИДАНИЕ ГОТОВНОСТИ КЭША...');
        
        let attempts = 0;
        while (!this.activeBinCache.isReady() && attempts < 30) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
            
            if (attempts % 5 === 0) {
                console.log(`⏳ ОЖИДАНИЕ КЭША: ${attempts}/30 попыток`);
                this.analyzer.getActiveBinStats();
            }
        }
        
        if (!this.activeBinCache.isReady()) {
            throw new Error('КЭШ НЕ ГОТОВ ПОСЛЕ 30 СЕКУНД');
        }
        
        console.log('✅ КЭШ ГОТОВ К РАБОТЕ');
        this.analyzer.getActiveBinStats();
    }

    /**
     * 🔥 ОСНОВНОЙ ЦИКЛ БОТА
     */
    startMainLoop() {
        console.log('🔄 ЗАПУСК ОСНОВНОГО ЦИКЛА');
        
        setInterval(() => {
            this.executeCycle();
        }, 2000); // Каждые 2 секунды
    }

    /**
     * 🔥 ВЫПОЛНЕНИЕ ОДНОГО ЦИКЛА
     */
    async executeCycle() {
        try {
            this.stats.cycles++;
            
            console.log(`\n🔄 ЦИКЛ ${this.stats.cycles}`);
            
            // 1. Проверяем готовность кэша
            if (!this.activeBinCache.isReady()) {
                console.log('⚠️ КЭШ НЕ ГОТОВ, ПРОПУСКАЕМ ЦИКЛ');
                return;
            }
            
            // 2. Анализируем арбитраж
            const analysis = this.analyzer.analyzeArbitrage(this.POOL_1, this.POOL_2);
            
            if (!analysis.profitable) {
                console.log(`❌ АРБИТРАЖ НЕ ВЫГОДЕН: ${analysis.error || 'малая разность цен'}`);
                return;
            }
            
            console.log(`✅ НАЙДЕН ВЫГОДНЫЙ АРБИТРАЖ:`);
            console.log(`   Pool 1: бин ${analysis.pool1.activeBinId}, цена ${analysis.pool1.price}`);
            console.log(`   Pool 2: бин ${analysis.pool2.activeBinId}, цена ${analysis.pool2.price}`);
            console.log(`   Разность: ${analysis.priceDiffPercent.toFixed(3)}%`);
            console.log(`   Направление: ${analysis.direction}`);
            
            // 3. Выполняем арбитраж
            await this.executeArbitrage(analysis);
            
        } catch (error) {
            console.error(`❌ ОШИБКА В ЦИКЛЕ ${this.stats.cycles}:`, error.message);
        }
    }

    /**
     * 🔥 ВЫПОЛНЕНИЕ АРБИТРАЖА
     */
    async executeArbitrage(analysis) {
        console.log('🔥 ВЫПОЛНЕНИЕ АРБИТРАЖА...');
        
        try {
            // Пример: создаем инструкции для арбитража
            const amountIn = 1000000; // 0.001 SOL
            
            if (analysis.direction === 'POOL1_TO_POOL2') {
                // Покупаем в Pool 1, продаем в Pool 2
                console.log('📈 ПОКУПАЕМ В POOL 1, ПРОДАЕМ В POOL 2');
                
                const buyInstruction = this.instructions.createSwap(this.POOL_1, amountIn, 0, false);
                const sellInstruction = this.instructions.createSwap(this.POOL_2, amountIn, 0, true);
                
                console.log(`   BUY в Pool 1: ${buyInstruction.data.length} bytes`);
                console.log(`   SELL в Pool 2: ${sellInstruction.data.length} bytes`);
                
            } else {
                // Покупаем в Pool 2, продаем в Pool 1
                console.log('📉 ПОКУПАЕМ В POOL 2, ПРОДАЕМ В POOL 1');
                
                const buyInstruction = this.instructions.createSwap(this.POOL_2, amountIn, 0, false);
                const sellInstruction = this.instructions.createSwap(this.POOL_1, amountIn, 0, true);
                
                console.log(`   BUY в Pool 2: ${buyInstruction.data.length} bytes`);
                console.log(`   SELL в Pool 1: ${sellInstruction.data.length} bytes`);
            }
            
            // TODO: Создать и отправить транзакцию
            console.log('✅ АРБИТРАЖ ВЫПОЛНЕН (СИМУЛЯЦИЯ)');
            
        } catch (error) {
            console.error('❌ ОШИБКА ВЫПОЛНЕНИЯ АРБИТРАЖА:', error.message);
        }
    }

    /**
     * 🔥 ТЕСТИРОВАНИЕ ВСЕХ ИНСТРУКЦИЙ
     */
    async testAllInstructions() {
        console.log('\n🧪 ТЕСТИРОВАНИЕ ВСЕХ ИНСТРУКЦИЙ ДЛЯ АКТИВНОГО БИНА');
        
        try {
            // 1. ADD_LIQUIDITY2 Pool 1
            const addLiq1 = this.instructions.createAddLiquidity2(this.POOL_1, 1000000, 0);
            console.log(`✅ ADD_LIQUIDITY2 Pool 1: ${addLiq1.length} bytes`);
            
            // 2. ADD_LIQUIDITY2 Pool 2
            const addLiq2 = this.instructions.createAddLiquidity2(this.POOL_2, 1000000, 0);
            console.log(`✅ ADD_LIQUIDITY2 Pool 2: ${addLiq2.length} bytes`);
            
            // 3. REMOVE_LIQUIDITY Pool 1
            const removeLiq1 = this.instructions.createRemoveLiquidity(this.POOL_1, 5000);
            console.log(`✅ REMOVE_LIQUIDITY Pool 1: ${removeLiq1.length} bytes`);
            
            // 4. REMOVE_LIQUIDITY Pool 2
            const removeLiq2 = this.instructions.createRemoveLiquidity(this.POOL_2, 5000);
            console.log(`✅ REMOVE_LIQUIDITY Pool 2: ${removeLiq2.length} bytes`);
            
            // 5. CLAIM_FEE2 Pool 1
            const claimFee1 = this.instructions.createClaimFee2(this.POOL_1);
            console.log(`✅ CLAIM_FEE2 Pool 1: ${claimFee1.length} bytes`);
            
            // 6. CLAIM_FEE2 Pool 2
            const claimFee2 = this.instructions.createClaimFee2(this.POOL_2);
            console.log(`✅ CLAIM_FEE2 Pool 2: ${claimFee2.length} bytes`);
            
            // 7. SWAP BUY SOL
            const swapBuy = this.instructions.createSwap(this.POOL_1, 1000000, 0, false);
            console.log(`✅ SWAP BUY SOL: ${swapBuy.data.length} bytes`);
            
            // 8. SWAP SELL SOL
            const swapSell = this.instructions.createSwap(this.POOL_1, 1000000, 0, true);
            console.log(`✅ SWAP SELL SOL: ${swapSell.data.length} bytes`);
            
            console.log('🎉 ВСЕ 8 ИНСТРУКЦИЙ УСПЕШНО СОЗДАНЫ ДЛЯ АКТИВНОГО БИНА!');
            
        } catch (error) {
            console.error('❌ ОШИБКА ТЕСТИРОВАНИЯ ИНСТРУКЦИЙ:', error.message);
        }
    }

    /**
     * 🔧 ЗАГРУЗКА RPC КОНФИГУРАЦИИ
     */
    loadRpcConfig() {
        try {
            return require('./rpc-config.js').config;
        } catch (error) {
            console.error('❌ ОШИБКА ЗАГРУЗКИ RPC CONFIG:', error.message);
            return null;
        }
    }

    /**
     * 🔥 ОСТАНОВКА БОТА
     */
    stop() {
        console.log('🛑 ОСТАНОВКА БОТА');
        this.activeBinCache.stopAutoUpdate();
        
        const runtime = Date.now() - this.stats.startTime;
        console.log(`📊 СТАТИСТИКА: ${this.stats.cycles} циклов за ${Math.round(runtime/1000)}с`);
    }
}

// Запуск если вызван напрямую
if (require.main === module) {
    const bot = new MeteoraActiveBinBot();
    
    // Запускаем бота
    bot.start().then(() => {
        console.log('🚀 БОТ ЗАПУЩЕН');
        
        // Тестируем инструкции через 5 секунд
        setTimeout(() => {
            bot.testAllInstructions();
        }, 5000);
        
    }).catch(error => {
        console.error('❌ ОШИБКА ЗАПУСКА:', error.message);
    });
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 ПОЛУЧЕН СИГНАЛ ОСТАНОВКИ');
        bot.stop();
        process.exit(0);
    });
}

module.exports = MeteoraActiveBinBot;
