/**
 * 🔍 METEORA REVERSE ENGINEER SEEDS
 * Обратный инжиниринг seeds для конкретных PDA из вашей инструкции
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');

// === CONFIGURATION ===
const CONFIG = {
  PROGRAM_ID: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
  
  // Ваши конкретные PDA
  POSITION: new PublicKey('Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC'),
  LB_PAIR: new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'), // WSOL-USDC
  BITMAP_EXTENSION: new PublicKey('4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH'),
  REMAINING_ACCOUNT: new PublicKey('Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw'),
  SENDER: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'),
  TOKEN_X_MINT: new PublicKey('So11111111111111111111111111111111111111112'), // WSOL
  TOKEN_Y_MINT: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), // USDC
};

// === SEED UTILITIES ===
class SeedUtils {
  static encodeString(str) {
    return Buffer.from(str, 'utf8');
  }
  
  static encodeBinId(binId) {
    return new BN(binId).toTwos(16).toArrayLike(Buffer, 'le', 2);
  }
  
  static encodeU16(value) {
    return new BN(value).toArrayLike(Buffer, 'le', 2);
  }
  
  static encodeU32(value) {
    return new BN(value).toArrayLike(Buffer, 'le', 4);
  }
  
  static encodeI64(value) {
    return new BN(value).toTwos(64).toArrayLike(Buffer, 'le', 8);
  }
  
  static encodeU64(value) {
    return new BN(value).toArrayLike(Buffer, 'le', 8);
  }
}

// === REVERSE ENGINEER ===
class ReverseEngineer {
  
  /**
   * 🔍 Брутфорс Position PDA с расширенными параметрами
   */
  static bruteforcePositionPDA() {
    console.log('\n🔍 БРУТФОРС POSITION PDA:');
    console.log(`Target: ${CONFIG.POSITION.toBase58()}`);
    
    const lbPair = CONFIG.LB_PAIR;
    const user = CONFIG.SENDER;
    
    // Пробуем разные префиксы
    const prefixes = ['position', 'Position', 'user_position', 'lb_position'];
    
    for (const prefix of prefixes) {
      console.log(`Пробуем prefix: "${prefix}"`);
      
      // Пробуем разные порядки seeds
      const seedCombinations = [
        // Стандартный порядок
        (index) => [
          SeedUtils.encodeString(prefix),
          lbPair.toBuffer(),
          user.toBuffer(),
          SeedUtils.encodeU16(index)
        ],
        // Обратный порядок user/lbPair
        (index) => [
          SeedUtils.encodeString(prefix),
          user.toBuffer(),
          lbPair.toBuffer(),
          SeedUtils.encodeU16(index)
        ],
        // Без index
        () => [
          SeedUtils.encodeString(prefix),
          lbPair.toBuffer(),
          user.toBuffer()
        ],
        // С u32 index
        (index) => [
          SeedUtils.encodeString(prefix),
          lbPair.toBuffer(),
          user.toBuffer(),
          SeedUtils.encodeU32(index)
        ]
      ];
      
      for (let combIndex = 0; combIndex < seedCombinations.length; combIndex++) {
        for (let positionIndex = 0; positionIndex < 20; positionIndex++) {
          try {
            const seeds = seedCombinations[combIndex](positionIndex);
            const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.PROGRAM_ID);
            
            if (pda.toBase58() === CONFIG.POSITION.toBase58()) {
              console.log(`✅ НАЙДЕН!`);
              console.log(`  Prefix: "${prefix}"`);
              console.log(`  Combination: ${combIndex}`);
              console.log(`  Position Index: ${positionIndex}`);
              console.log(`  Bump: ${bump}`);
              console.log(`  Seeds:`);
              seeds.forEach((seed, i) => {
                console.log(`    [${i}] ${seed.toString('hex')} (${seed.length} bytes)`);
              });
              return { prefix, combIndex, positionIndex, bump, seeds };
            }
          } catch (e) {
            // Игнорируем ошибки
          }
        }
      }
    }
    
    console.log('❌ Position PDA не найден');
    return null;
  }
  
  /**
   * 🔍 Брутфорс Bitmap Extension PDA
   */
  static bruteForceBitmapExtension() {
    console.log('\n🔍 БРУТФОРС BITMAP EXTENSION PDA:');
    console.log(`Target: ${CONFIG.BITMAP_EXTENSION.toBase58()}`);
    
    const lbPair = CONFIG.LB_PAIR;
    const prefixes = ['bitmap_extension', 'BitmapExtension', 'bin_array_bitmap_extension', 'bitmap'];
    
    for (const prefix of prefixes) {
      console.log(`Пробуем prefix: "${prefix}"`);
      
      // Пробуем разные диапазоны binId
      for (let binId = -5000; binId <= 5000; binId++) {
        // Пробуем разные выравнивания
        const alignments = [1, 64, 128, 256, 512];
        
        for (const alignment of alignments) {
          const alignedBinId = Math.floor(binId / alignment) * alignment;
          
          const seedCombinations = [
            [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeBinId(alignedBinId)],
            [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeU16(alignedBinId)],
            [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeU32(alignedBinId)],
            [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeI64(alignedBinId)]
          ];
          
          for (let combIndex = 0; combIndex < seedCombinations.length; combIndex++) {
            try {
              const seeds = seedCombinations[combIndex];
              const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.PROGRAM_ID);
              
              if (pda.toBase58() === CONFIG.BITMAP_EXTENSION.toBase58()) {
                console.log(`✅ НАЙДЕН!`);
                console.log(`  Prefix: "${prefix}"`);
                console.log(`  Bin ID: ${binId}`);
                console.log(`  Alignment: ${alignment}`);
                console.log(`  Aligned Bin ID: ${alignedBinId}`);
                console.log(`  Encoding: ${combIndex}`);
                console.log(`  Bump: ${bump}`);
                console.log(`  Seeds:`);
                seeds.forEach((seed, i) => {
                  console.log(`    [${i}] ${seed.toString('hex')} (${seed.length} bytes)`);
                });
                return { prefix, binId, alignment, alignedBinId, combIndex, bump, seeds };
              }
            } catch (e) {
              // Игнорируем ошибки
            }
          }
        }
      }
    }
    
    console.log('❌ Bitmap Extension PDA не найден');
    return null;
  }
  
  /**
   * 🔍 Брутфорс Remaining Account (Bin Array)
   */
  static bruteforceBinArray() {
    console.log('\n🔍 БРУТФОРС REMAINING ACCOUNT (BIN ARRAY):');
    console.log(`Target: ${CONFIG.REMAINING_ACCOUNT.toBase58()}`);
    
    const lbPair = CONFIG.LB_PAIR;
    const prefixes = ['bin_array', 'BinArray', 'bin', 'array'];
    
    for (const prefix of prefixes) {
      console.log(`Пробуем prefix: "${prefix}"`);
      
      // Пробуем разные диапазоны
      for (let value = -1000; value <= 1000; value++) {
        const seedCombinations = [
          [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeI64(value)],
          [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeU64(value)],
          [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeU32(value)],
          [SeedUtils.encodeString(prefix), lbPair.toBuffer(), SeedUtils.encodeBinId(value)]
        ];
        
        for (let combIndex = 0; combIndex < seedCombinations.length; combIndex++) {
          try {
            const seeds = seedCombinations[combIndex];
            const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.PROGRAM_ID);
            
            if (pda.toBase58() === CONFIG.REMAINING_ACCOUNT.toBase58()) {
              console.log(`✅ НАЙДЕН!`);
              console.log(`  Prefix: "${prefix}"`);
              console.log(`  Value: ${value}`);
              console.log(`  Encoding: ${combIndex}`);
              console.log(`  Bump: ${bump}`);
              console.log(`  Seeds:`);
              seeds.forEach((seed, i) => {
                console.log(`    [${i}] ${seed.toString('hex')} (${seed.length} bytes)`);
              });
              return { prefix, value, combIndex, bump, seeds };
            }
          } catch (e) {
            // Игнорируем ошибки
          }
        }
      }
    }
    
    console.log('❌ Bin Array PDA не найден');
    return null;
  }
  
  /**
   * 🔍 Анализ Reserve PDA (они должны работать по стандартной формуле)
   */
  static analyzeReservePDAs() {
    console.log('\n🔍 АНАЛИЗ RESERVE PDA:');
    
    const lbPair = CONFIG.LB_PAIR;
    const tokens = [CONFIG.TOKEN_X_MINT, CONFIG.TOKEN_Y_MINT];
    const tokenNames = ['WSOL', 'USDC'];
    
    for (let i = 0; i < tokens.length; i++) {
      const seeds = [
        SeedUtils.encodeString('reserve'),
        lbPair.toBuffer(),
        tokens[i].toBuffer()
      ];
      
      const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.PROGRAM_ID);
      
      console.log(`${tokenNames[i]} Reserve: ${pda.toBase58()} (bump: ${bump})`);
    }
  }
}

// === MAIN EXECUTION ===
async function main() {
  console.log('🔍 METEORA REVERSE ENGINEER SEEDS');
  console.log('Обратный инжиниринг seeds для ваших конкретных PDA');
  console.log('='.repeat(60));
  
  try {
    // Анализируем каждый PDA
    const positionResult = ReverseEngineer.bruteforcePositionPDA();
    const bitmapResult = ReverseEngineer.bruteForceBitmapExtension();
    const binArrayResult = ReverseEngineer.bruteforceBinArray();
    
    // Анализируем Reserve PDA
    ReverseEngineer.analyzeReservePDAs();
    
    console.log('\n📊 СВОДКА РЕЗУЛЬТАТОВ:');
    console.log('='.repeat(60));
    console.log(`Position PDA:        ${positionResult ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
    console.log(`Bitmap Extension:    ${bitmapResult ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
    console.log(`Bin Array:           ${binArrayResult ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
    
    if (positionResult || bitmapResult || binArrayResult) {
      console.log('\n🎯 Найденные формулы можно использовать для создания правильной транзакции!');
    }
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  ReverseEngineer,
  CONFIG
};
