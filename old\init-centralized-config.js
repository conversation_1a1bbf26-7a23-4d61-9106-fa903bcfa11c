/**
 * 🔥 ИНИЦИАЛИЗАЦИЯ ЦЕНТРАЛИЗОВАННОЙ КОНФИГУРАЦИИ
 * ПРОВЕРЯЕТ ВСЕ RPC КЛЮЧИ И НАСТРОЙКИ
 */

const rpcConfig = require('./rpc-config.js');
const { globalRPCManager } = require('./centralized-rpc-manager.js');

async function initializeCentralizedConfig() {
    console.log('🔥 ИНИЦИАЛИЗАЦИЯ ЦЕНТРАЛИЗОВАННОЙ КОНФИГУРАЦИИ...\n');

    try {
        // 1. ПРОВЕРКА RPC КОНФИГУРАЦИИ
        console.log('📊 ПРОВЕРКА RPC КОНФИГУРАЦИИ:');
        const stats = rpcConfig.getStats();
        console.log(`   ✅ Transaction RPC: ${stats.transactionRPC}`);
        console.log(`   ✅ Data RPC провайдеров: ${stats.dataRPCs}`);
        console.log(`   ✅ Активные провайдеры: ${stats.enabledProviders.join(', ')}`);
        console.log(`   ✅ Wallet настроен: ${stats.walletConfigured ? 'ДА' : 'НЕТ'}\n`);

        // 2. ПРОВЕРКА TRANSACTION RPC
        console.log('🚀 ПРОВЕРКА TRANSACTION RPC:');
        const transactionRPC = rpcConfig.getTransactionRPC();
        console.log(`   URL: ${transactionRPC.url}`);
        console.log(`   Название: ${transactionRPC.name}`);
        console.log(`   Лимит запросов: ${transactionRPC.maxRequestsPerSecond}/сек`);
        console.log(`   Timeout: ${transactionRPC.timeout}мс\n`);

        // 3. ПРОВЕРКА DATA RPC ПРОВАЙДЕРОВ
        console.log('📊 ПРОВЕРКА DATA RPC ПРОВАЙДЕРОВ:');
        const dataRPCs = rpcConfig.getDataRPCs();
        dataRPCs.forEach((rpc, index) => {
            console.log(`   ${index + 1}. ${rpc.name}`);
            console.log(`      URL: ${rpc.url}`);
            console.log(`      Приоритет: ${rpc.priority}`);
            console.log(`      Лимит: ${rpc.maxRequestsPerSecond}/сек`);
            console.log(`      Timeout: ${rpc.timeout}мс`);
        });
        console.log('');

        // 4. ПРОВЕРКА WALLET КОНФИГУРАЦИИ
        console.log('🔑 ПРОВЕРКА WALLET КОНФИГУРАЦИИ:');
        const walletConfig = rpcConfig.getWalletConfig();
        console.log(`   Address: ${walletConfig.address ? walletConfig.address.slice(0, 20) + '...' : 'НЕ НАСТРОЕН'}`);
        console.log(`   Private Key: ${walletConfig.privateKey ? 'НАСТРОЕН' : 'НЕ НАСТРОЕН'}`);
        console.log(`   Seed Phrase: ${walletConfig.seedPhrase ? 'НАСТРОЕН' : 'НЕ НАСТРОЕН'}\n`);

        // 5. ПРОВЕРКА MARGINFI КОНФИГУРАЦИИ
        console.log('🏦 ПРОВЕРКА MARGINFI КОНФИГУРАЦИИ:');
        const marginfiConfig = rpcConfig.getMarginfiConfig();
        console.log(`   Program ID: ${marginfiConfig.programId}`);
        console.log(`   Group: ${marginfiConfig.group}\n`);

        // 6. ПРОВЕРКА METEORA КОНФИГУРАЦИИ
        console.log('🌪️ ПРОВЕРКА METEORA КОНФИГУРАЦИИ:');
        const meteoraConfig = rpcConfig.getMeteoraConfig();
        console.log(`   Program ID: ${meteoraConfig.programId}\n`);

        // 7. ТЕСТ RPC МЕНЕДЖЕРА
        console.log('🔄 ТЕСТ RPC МЕНЕДЖЕРА:');
        try {
            const dataConnection = await globalRPCManager.getConnectionByType('data');
            console.log('   ✅ Data connection получен успешно');
            
            const transactionConnection = await globalRPCManager.getConnectionByType('transaction');
            console.log('   ✅ Transaction connection получен успешно');
        } catch (error) {
            console.log(`   ❌ Ошибка RPC менеджера: ${error.message}`);
        }

        console.log('\n🎉 ЦЕНТРАЛИЗОВАННАЯ КОНФИГУРАЦИЯ ИНИЦИАЛИЗИРОВАНА УСПЕШНО!');
        console.log('🔥 ВСЕ RPC КЛЮЧИ ЦЕНТРАЛИЗОВАНЫ В rpc-config.js');
        console.log('📊 ВСЕ ФАЙЛЫ ИСПОЛЬЗУЮТ ТОЛЬКО ЦЕНТРАЛИЗОВАННУЮ КОНФИГУРАЦИЮ!');

        return true;

    } catch (error) {
        console.error('❌ ОШИБКА ИНИЦИАЛИЗАЦИИ КОНФИГУРАЦИИ:', error.message);
        console.error('🔧 ПРОВЕРЬ ФАЙЛ .env.solana И НАСТРОЙКИ!');
        return false;
    }
}

// Запуск если файл вызван напрямую
if (require.main === module) {
    initializeCentralizedConfig()
        .then(success => {
            if (success) {
                console.log('\n✅ ГОТОВО! КОНФИГУРАЦИЯ РАБОТАЕТ!');
                process.exit(0);
            } else {
                console.log('\n❌ ОШИБКА! ИСПРАВЬ КОНФИГУРАЦИЮ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { initializeCentralizedConfig };
