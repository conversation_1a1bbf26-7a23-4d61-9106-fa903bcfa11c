/**
 * 🔥 ПРОВЕРКА BIN ARRAYS ДЛЯ РАБОЧИХ ПУЛОВ
 * ГЕНЕРИРУЕМ И ПРОВЕРЯЕМ BIN ARRAYS ДЛЯ POOL 1 И POOL 2
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function checkBinArrays() {
    console.log('🔥 ПРОВЕРКА BIN ARRAYS ДЛЯ РАБОЧИХ ПУЛОВ...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // Meteora Program ID
        const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Получаем пулы
        const pool1 = rpcConfig.getMeteoraPool1();
        const pool2 = rpcConfig.getMeteoraPool2();

        // Функция генерации Bin Array PDA
        function generateBinArrayPDA(lbPair, binArrayIndex) {
            const lbPairKey = new PublicKey(lbPair);
            
            // Конвертируем index в little-endian bytes
            const indexBuffer = Buffer.alloc(8);
            indexBuffer.writeBigInt64LE(BigInt(binArrayIndex), 0);
            
            const [binArray] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    lbPairKey.toBuffer(),
                    indexBuffer
                ],
                METEORA_PROGRAM_ID
            );
            
            return binArray;
        }

        // Функция проверки Bin Array
        async function checkBinArray(poolName, lbPair, index) {
            try {
                const binArray = generateBinArrayPDA(lbPair, index);
                console.log(`🔍 ${poolName} - Bin Array ${index}:`);
                console.log(`   PDA: ${binArray.toString()}`);
                
                const accountInfo = await connection.getAccountInfo(binArray);
                
                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   💰 Lamports: ${accountInfo.lamports}`);
                    console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
                    
                    if (accountInfo.owner.equals(METEORA_PROGRAM_ID)) {
                        console.log(`   ✅ ПРИНАДЛЕЖИТ METEORA!`);
                    }
                    
                    return { index, address: binArray.toString(), exists: true, size: accountInfo.data.length };
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ`);
                    return { index, address: binArray.toString(), exists: false };
                }
                
            } catch (error) {
                console.log(`   💥 Ошибка: ${error.message}`);
                return { index, address: null, exists: false, error: error.message };
            }
        }

        // Проверяем Bin Arrays для разных индексов
        const indicesToCheck = [-2, -1, 0, 1, 2, 3, 4, 5];
        
        console.log('🔥 ПРОВЕРКА BIN ARRAYS ДЛЯ POOL 1:');
        console.log(`LB Pair: ${pool1.lbPair}\n`);
        
        const pool1BinArrays = [];
        for (const index of indicesToCheck) {
            const result = await checkBinArray('Pool 1', pool1.lbPair, index);
            pool1BinArrays.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('🔥 ПРОВЕРКА BIN ARRAYS ДЛЯ POOL 2:');
        console.log(`LB Pair: ${pool2.lbPair}\n`);
        
        const pool2BinArrays = [];
        for (const index of indicesToCheck) {
            const result = await checkBinArray('Pool 2', pool2.lbPair, index);
            pool2BinArrays.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ИТОГОВЫЙ ОТЧЁТ
        console.log('🎯 ИТОГОВЫЙ ОТЧЁТ BIN ARRAYS:');
        
        const pool1Existing = pool1BinArrays.filter(b => b.exists);
        const pool2Existing = pool2BinArrays.filter(b => b.exists);
        
        console.log(`\n📊 POOL 1 (${pool1Existing.length}/${pool1BinArrays.length} существуют):`);
        pool1Existing.forEach(bin => {
            console.log(`   ✅ Index ${bin.index}: ${bin.address} (${bin.size} байт)`);
        });
        
        console.log(`\n📊 POOL 2 (${pool2Existing.length}/${pool2BinArrays.length} существуют):`);
        pool2Existing.forEach(bin => {
            console.log(`   ✅ Index ${bin.index}: ${bin.address} (${bin.size} байт)`);
        });

        // Рекомендации
        if (pool1Existing.length > 0 || pool2Existing.length > 0) {
            console.log('\n🎉 НАЙДЕНЫ РАБОЧИЕ BIN ARRAYS!');
            console.log('🔥 ИСПОЛЬЗУЙ ЭТИ АДРЕСА В add_liquidity2!');
            
            if (pool1Existing.length > 0) {
                console.log(`\n🔥 POOL 1 - РЕКОМЕНДУЕМЫЕ BIN ARRAYS:`);
                pool1Existing.slice(0, 3).forEach(bin => {
                    console.log(`   Index ${bin.index}: ${bin.address}`);
                });
            }
            
            if (pool2Existing.length > 0) {
                console.log(`\n🔥 POOL 2 - РЕКОМЕНДУЕМЫЕ BIN ARRAYS:`);
                pool2Existing.slice(0, 3).forEach(bin => {
                    console.log(`   Index ${bin.index}: ${bin.address}`);
                });
            }
        } else {
            console.log('\n❌ BIN ARRAYS НЕ НАЙДЕНЫ!');
            console.log('🔧 НУЖНО СОЗДАТЬ BIN ARRAYS ЧЕРЕЗ initialize_bin_array');
        }

        return {
            pool1: pool1BinArrays,
            pool2: pool2BinArrays,
            pool1Existing: pool1Existing,
            pool2Existing: pool2Existing
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск проверки
if (require.main === module) {
    checkBinArrays()
        .then(result => {
            if (result && (result.pool1Existing.length > 0 || result.pool2Existing.length > 0)) {
                console.log('\n✅ BIN ARRAYS НАЙДЕНЫ!');
                process.exit(0);
            } else {
                console.log('\n❌ BIN ARRAYS НЕ НАЙДЕНЫ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { checkBinArrays };
