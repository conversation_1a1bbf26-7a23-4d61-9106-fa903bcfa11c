# 🌪️ BMETEORA.js - Полная архитектура системы арбитража

## 📋 Обзор системы

**BMETEORA.js** - это сложная система flash loan арбитража на Solana с интеграцией Meteora DLMM пулов и MarginFi займов. Система использует умный анализ ликвидности и автоматическое сжатие транзакций через ALT таблицы.

**Цель:** $100,000 за 24 часа через внутренний арбитраж между 3 Meteora пулами

## 🏗️ Основные компоненты

### 🔥 Главные файлы

| Файл | Описание | Роль |
|------|----------|------|
| **BMETEORA.js** | Основной бот арбитража | Главный контроллер, мониторинг цен, запуск арбитража |
| **complete-flash-loan-structure.js** | Создание всех инструкций | Сборка 18 инструкций flash loan транзакции |
| **trading-config.js** | Централизованная конфигурация | Все настройки торговли в одном месте |

### 🚀 Менеджеры и кэши

| Файл | Описание | Функции |
|------|----------|---------|
| **meteora-bin-cache-manager-clean.js** | Кэш активных бинов | Получение цен через getBinsAroundActiveBin(1,1) |
| **centralized-rpc-manager.js** | Управление RPC подключениями | Равномерное распределение нагрузки, fallback |
| **centralized-amount-converter.js** | Конвертация сумм | USD ↔ Native amounts, форматирование |

### 🧠 Анализаторы

| Файл | Описание | Назначение |
|------|----------|------------|
| **smart-liquidity-analyzer.js** | Умный анализ ликвидности | Анализ 3 бинов каждого пула, расчет оптимальных сумм |
| **meteora-fee-analyzer.js** | Анализ комиссий пулов | Получение реальных комиссий каждого пула |

### 📦 Внешние зависимости

| SDK | Версия | Использование |
|-----|--------|---------------|
| @meteora-ag/dlmm | ^1.5.4 | Работа с Meteora DLMM пулами |
| @mrgnlabs/marginfi-client-v2 | ^6.1.0 | MarginFi flash loans |
| @solana/web3.js | ^1.98.2 | Solana blockchain интеграция |
| @jup-ag/api | ^6.0.44 | Jupiter API для свопов |

## 🔄 Поток выполнения

### 1. Инициализация (BMETEORA.js)
```javascript
// Загрузка конфигурации
const { TRADING_CONFIG } = require('./trading-config.js');

// Инициализация компонентов
this.binCacheManager = new MeteoraBinCacheManager();
this.completeFlashLoanStructure = new CompleteFlashLoanStructure();
this.feeAnalyzer = new MeteoraFeeAnalyzer();
```

### 2. Мониторинг цен (каждые 600ms)
```javascript
// Получение точных цен из активных бинов
const prices = await this.getExactPricesFromActiveBins();

// Анализ спредов
const spread = (mostExpensive.price - cheapest.price) / cheapest.price * 100;
```

### 3. Выполнение арбитража
```javascript
// Умный анализ ликвидности
await this.completeFlashLoanStructure.performSmartAnalysis();

// Создание транзакции
const result = await this.completeFlashLoanStructure.createCompleteFlashLoanTransactionWithALT();
```

## 📊 Структура транзакции (18 инструкций)

### ComputeBudget инструкции (2)
1. `setComputeUnitLimit` - Лимит compute units
2. `setComputeUnitPrice` - Цена compute units

### MarginFi Borrow инструкции (2)
3. `lendingAccountBorrow` - Займ USDC
4. `lendingAccountBorrow` - Займ WSOL

### Meteora Swap инструкции (4)
5. `swap` - Первый своп (Pool 1)
6. `swap` - Второй своп (Pool 2)
7. `swap` - Третий своп (обратно)
8. `swap` - Четвертый своп (закрытие)

### Meteora Add Liquidity инструкции (6)
9. `addLiquidityByStrategy` - Pool 1, бин -1
10. `addLiquidityByStrategy` - Pool 1, активный бин
11. `addLiquidityByStrategy` - Pool 1, бин +1
12. `addLiquidityByStrategy` - Pool 2, бин -1
13. `addLiquidityByStrategy` - Pool 2, активный бин
14. `addLiquidityByStrategy` - Pool 2, бин +1

### MarginFi Repay инструкции (4)
15. `lendingAccountRepay` - Возврат USDC
16. `lendingAccountRepay` - Возврат WSOL
17. `lendingAccountRepay` - Возврат процентов USDC
18. `lendingAccountRepay` - Возврат процентов WSOL

## 🗜️ ALT сжатие транзакций

### ALT таблицы (2 основные)
- **MarginFi ALT**: `HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC` (256 адресов)
- **Custom ALT**: `FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe` (106 адресов)

### Процесс сжатия
1. Загрузка ALT таблиц через `loadALTTablesDirectly()`
2. Замена адресов на индексы в ALT
3. Создание VersionedTransaction с AddressLookupTableAccounts
4. Проверка размера транзакции (лимит 1232 байта)

## 🎯 Конфигурация (trading-config.js)

### Основные настройки
```javascript
const TRADING_CONFIG = {
  MIN_SPREAD_PERCENT: 0.005,        // 0.05% минимальный спред
  PROTOCOL_FEE_PERCENT: 0.003,      // 0.003% единственная комиссия
  FLASH_LOAN_AMOUNT_USD: 1000,      // $1K минимальная позиция
  MAX_FLASH_LOAN_AMOUNT_USD: 200000, // $200K максимальный займ
  CYCLE_DELAY_MS: 5000,             // 5 сек между циклами
  TRANSACTION_SEND_INTERVAL_MS: 1000 // 1 сек между отправками
};
```

### Meteora пулы
```javascript
METEORA_POOLS: {
  POOL_1: {
    address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    name: 'meteora1'
  },
  POOL_2: {
    address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
    name: 'meteora2'
  }
}
```

## 🔐 Переменные окружения (.env.solana)

```bash
# RPC endpoints
QUICKNODE_RPC_URL=https://your-quicknode-url
HELIUS_RPC_URL=https://your-helius-url

# Wallet
WALLET_PATH=./wallet.json

# MarginFi
MARGINFI_ACCOUNT=your-marginfi-account-address
```

## 📁 Структура файлов

```
DEXSWAP1/
├── BMETEORA.js                           # 🌪️ Основной бот
├── complete-flash-loan-structure.js      # 🔥 Создание инструкций
├── trading-config.js                     # 📋 Конфигурация
├── meteora-bin-cache-manager-clean.js    # 🚀 Кэш бинов
├── centralized-rpc-manager.js            # 🌐 RPC менеджер
├── centralized-amount-converter.js       # 💰 Конвертер сумм
├── smart-liquidity-analyzer.js           # 🧠 Умный анализатор
├── meteora-fee-analyzer.js               # 🔍 Анализатор комиссий
├── wallet.json                           # 🔑 Приватный ключ
├── custom-alt-data.json                  # 🗜️ ALT таблицы
├── .env.solana                           # 🔐 Переменные окружения
└── package.json                          # 📦 Зависимости
```

## 🚀 Запуск системы

```bash
# Установка зависимостей
npm install

# Запуск основного бота
node BMETEORA.js
```

## 🎯 Ключевые особенности

### ✅ Преимущества
- **Автоматический мониторинг** цен каждые 600ms
- **Умный анализ ликвидности** 3 бинов каждого пула
- **ALT сжатие** транзакций для экономии размера
- **Централизованная конфигурация** всех параметров
- **Защита от rate limiting** с интервалами между запросами
- **Реальные комиссии** каждого пула через анализатор

### 🔧 Технические решения
- **Кэширование активных бинов** для быстрого доступа к ценам
- **Равномерное распределение RPC нагрузки** между провайдерами
- **Автоматическое восстановление** после ошибок
- **Динамический расчет** оптимальных сумм займов

### 🛡️ Безопасность
- **Проверка спредов** перед выполнением арбитража
- **Лимиты на размер** позиций и займов
- **Таймауты** для предотвращения зависания
- **Логирование** всех операций для отладки

Эта архитектура обеспечивает стабильную и эффективную работу системы арбитража с минимальными рисками и максимальной прибыльностью.
