/**
 * 🎯 METEORA TARGETED INTERCEPTOR
 * ЦЕЛЕНАПРАВЛЕННЫЙ ПОИСК ВАШИХ КОНКРЕТНЫХ PDA
 * Ищет точные формулы для ваших PDA из инструкции
 */

const { PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');
const fs = require('fs');

// === CONFIGURATION ===
const CONFIG = {
  METEORA_PROGRAM_ID: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
  
  // Ваши точные PDA из инструкции
  TARGET_PDAS: {
    position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
    bitmapExtension: '4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH',
    remainingAccount: 'Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw'
  },
  
  // Ваши параметры
  LB_PAIR: new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'),
  USER: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'),
  TOKEN_X: new PublicKey('So11111111111111111111111111111111111111112'), // WSOL
  TOKEN_Y: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') // USDC
};

// === SEED UTILITIES ===
class SeedUtils {
  static encodeString(str) {
    return Buffer.from(str, 'utf8');
  }
  
  static encodeI16(value) {
    const buf = Buffer.alloc(2);
    buf.writeInt16LE(value, 0);
    return buf;
  }
  
  static encodeU16(value) {
    const buf = Buffer.alloc(2);
    buf.writeUInt16LE(value, 0);
    return buf;
  }
  
  static encodeI32(value) {
    const buf = Buffer.alloc(4);
    buf.writeInt32LE(value, 0);
    return buf;
  }
  
  static encodeU32(value) {
    const buf = Buffer.alloc(4);
    buf.writeUInt32LE(value, 0);
    return buf;
  }
  
  static encodeI64(value) {
    const buf = Buffer.alloc(8);
    buf.writeBigInt64LE(BigInt(value), 0);
    return buf;
  }
  
  static encodeU64(value) {
    const buf = Buffer.alloc(8);
    buf.writeBigUInt64LE(BigInt(value), 0);
    return buf;
  }
}

// === TARGETED SEARCHER ===
class TargetedPDASearcher {
  constructor() {
    this.foundFormulas = {};
  }

  /**
   * 🎯 ПОИСК POSITION PDA
   */
  searchPositionPDA() {
    console.log('\n🎯 ПОИСК POSITION PDA:');
    console.log(`Target: ${CONFIG.TARGET_PDAS.position}`);
    
    const prefixes = [
      'position', 'Position', 'user_position', 'lb_position', 'pos',
      'user_pos', 'liquidity_position', 'lp_position', 'user', 'account',
      'user_account', 'lb_user', 'dlmm_position', 'meteora_position'
    ];
    
    const lbPair = CONFIG.LB_PAIR;
    const user = CONFIG.USER;
    
    for (const prefix of prefixes) {
      console.log(`  Тестируем prefix: "${prefix}"`);
      
      // Разные порядки параметров
      const orders = [
        (p, lp, u, i) => [p, lp, u, i],     // prefix, lbPair, user, index
        (p, lp, u, i) => [p, u, lp, i],     // prefix, user, lbPair, index
        (p, lp, u, i) => [p, lp, u],        // prefix, lbPair, user (без index)
        (p, lp, u, i) => [p, u, lp],        // prefix, user, lbPair (без index)
        (p, lp, u, i) => [p, i, lp, u],     // prefix, index, lbPair, user
        (p, lp, u, i) => [p, i, u, lp]      // prefix, index, user, lbPair
      ];
      
      for (let orderIndex = 0; orderIndex < orders.length; orderIndex++) {
        for (let positionIndex = 0; positionIndex < 200; positionIndex++) {
          // Разные кодирования индекса
          const indexEncodings = [
            SeedUtils.encodeU16(positionIndex),
            SeedUtils.encodeI16(positionIndex),
            SeedUtils.encodeU32(positionIndex),
            SeedUtils.encodeI32(positionIndex),
            SeedUtils.encodeU64(positionIndex),
            SeedUtils.encodeI64(positionIndex),
            Buffer.from([positionIndex]) // u8
          ];
          
          for (const indexEncoding of indexEncodings) {
            try {
              const seeds = orders[orderIndex](
                SeedUtils.encodeString(prefix),
                lbPair.toBuffer(),
                user.toBuffer(),
                indexEncoding
              ).filter(Boolean);
              
              const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.METEORA_PROGRAM_ID);
              
              if (pda.toBase58() === CONFIG.TARGET_PDAS.position) {
                console.log(`🎯 НАЙДЕН POSITION PDA!`);
                console.log(`  Prefix: "${prefix}"`);
                console.log(`  Order: ${orderIndex}`);
                console.log(`  Position Index: ${positionIndex}`);
                console.log(`  Index Encoding: ${indexEncoding.toString('hex')} (${indexEncoding.length} bytes)`);
                console.log(`  Bump: ${bump}`);
                console.log(`  Seeds:`);
                seeds.forEach((seed, i) => {
                  console.log(`    [${i}] ${this.decodeSeed(seed)}`);
                });
                
                this.foundFormulas.position = {
                  prefix,
                  orderIndex,
                  positionIndex,
                  indexEncoding: indexEncoding.toString('hex'),
                  bump,
                  seeds: seeds.map(s => s.toString('hex'))
                };
                
                return true;
              }
            } catch (e) {
              // Игнорируем ошибки
            }
          }
        }
      }
    }
    
    console.log('❌ Position PDA не найден');
    return false;
  }

  /**
   * 🗺️ ПОИСК BITMAP EXTENSION PDA
   */
  searchBitmapExtensionPDA() {
    console.log('\n🗺️ ПОИСК BITMAP EXTENSION PDA:');
    console.log(`Target: ${CONFIG.TARGET_PDAS.bitmapExtension}`);
    
    const prefixes = [
      'bitmap_extension', 'BitmapExtension', 'bin_array_bitmap_extension',
      'bitmap', 'bin_bitmap', 'array_bitmap'
    ];
    
    const lbPair = CONFIG.LB_PAIR;
    
    // Расширенный диапазон binId значений
    const probableBinIds = [];

    // Добавляем широкий диапазон значений
    for (let i = -10000; i <= 10000; i += 64) {
      probableBinIds.push(i);
    }

    // Добавляем специфические значения из контекста
    const specificValues = [
      -4517, -4516, -4518, -4515, -4519, // Центральные значения
      -512, -256, 0, 256, 512, // Стандартные выравнивания
      -1024, -768, -640, -384, -128, // Другие возможные
      -71 * 64, -71 * 70, -71 * 256 // На основе найденного bin_array index -71
    ];

    specificValues.forEach(val => {
      if (!probableBinIds.includes(val)) {
        probableBinIds.push(val);
      }
    });
    
    for (const prefix of prefixes) {
      console.log(`  Тестируем prefix: "${prefix}"`);
      
      for (const baseBinId of probableBinIds) {
        // Разные выравнивания
        const alignments = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024];
        
        for (const alignment of alignments) {
          const alignedBinId = Math.floor(baseBinId / alignment) * alignment;
          
          // Разные кодирования (с проверками диапазонов)
          const encodings = [];

          // i16: -32768 to 32767
          if (alignedBinId >= -32768 && alignedBinId <= 32767) {
            encodings.push({ type: 'i16', buffer: SeedUtils.encodeI16(alignedBinId) });
          }

          // u16: 0 to 65535
          if (alignedBinId >= 0 && alignedBinId <= 65535) {
            encodings.push({ type: 'u16', buffer: SeedUtils.encodeU16(alignedBinId) });
          }

          // i32: всегда подходит для наших значений
          encodings.push({ type: 'i32', buffer: SeedUtils.encodeI32(alignedBinId) });

          // u32: только для положительных
          if (alignedBinId >= 0) {
            encodings.push({ type: 'u32', buffer: SeedUtils.encodeU32(alignedBinId) });
          }
          
          for (const encoding of encodings) {
            try {
              const seeds = [
                SeedUtils.encodeString(prefix),
                lbPair.toBuffer(),
                encoding.buffer
              ];
              
              const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.METEORA_PROGRAM_ID);
              
              if (pda.toBase58() === CONFIG.TARGET_PDAS.bitmapExtension) {
                console.log(`🎯 НАЙДЕН BITMAP EXTENSION PDA!`);
                console.log(`  Prefix: "${prefix}"`);
                console.log(`  Base Bin ID: ${baseBinId}`);
                console.log(`  Alignment: ${alignment}`);
                console.log(`  Aligned Bin ID: ${alignedBinId}`);
                console.log(`  Encoding: ${encoding.type}`);
                console.log(`  Bump: ${bump}`);
                console.log(`  Seeds:`);
                seeds.forEach((seed, i) => {
                  console.log(`    [${i}] ${this.decodeSeed(seed)}`);
                });
                
                this.foundFormulas.bitmapExtension = {
                  prefix,
                  baseBinId,
                  alignment,
                  alignedBinId,
                  encoding: encoding.type,
                  bump,
                  seeds: seeds.map(s => s.toString('hex'))
                };
                
                return true;
              }
            } catch (e) {
              // Игнорируем ошибки
            }
          }
        }
      }
    }
    
    console.log('❌ Bitmap Extension PDA не найден');
    return false;
  }

  /**
   * 🗃️ ПОИСК REMAINING ACCOUNT (BIN ARRAY) PDA
   */
  searchRemainingAccountPDA() {
    console.log('\n🗃️ ПОИСК REMAINING ACCOUNT PDA:');
    console.log(`Target: ${CONFIG.TARGET_PDAS.remainingAccount}`);
    
    const prefixes = [
      'bin_array', 'BinArray', 'bin', 'array', 'bin_data',
      'liquidity_bin', 'lb_bin', 'bin_storage'
    ];
    
    const lbPair = CONFIG.LB_PAIR;
    
    for (const prefix of prefixes) {
      console.log(`  Тестируем prefix: "${prefix}"`);
      
      // Пробуем разные индексы/значения
      for (let value = -200; value <= 200; value++) {
        // Разные кодирования (с проверками диапазонов)
        const encodings = [];

        // i64: всегда подходит
        encodings.push({ type: 'i64', buffer: SeedUtils.encodeI64(value) });

        // u64: только для положительных
        if (value >= 0) {
          encodings.push({ type: 'u64', buffer: SeedUtils.encodeU64(value) });
        }

        // i32: -********** to **********
        if (value >= -********** && value <= **********) {
          encodings.push({ type: 'i32', buffer: SeedUtils.encodeI32(value) });
        }

        // u32: 0 to 4294967295
        if (value >= 0 && value <= 4294967295) {
          encodings.push({ type: 'u32', buffer: SeedUtils.encodeU32(value) });
        }

        // i16: -32768 to 32767
        if (value >= -32768 && value <= 32767) {
          encodings.push({ type: 'i16', buffer: SeedUtils.encodeI16(value) });
        }

        // u16: 0 to 65535
        if (value >= 0 && value <= 65535) {
          encodings.push({ type: 'u16', buffer: SeedUtils.encodeU16(value) });
        }
        
        for (const encoding of encodings) {
          if (!encoding.buffer) continue;
          
          try {
            const seeds = [
              SeedUtils.encodeString(prefix),
              lbPair.toBuffer(),
              encoding.buffer
            ];
            
            const [pda, bump] = PublicKey.findProgramAddressSync(seeds, CONFIG.METEORA_PROGRAM_ID);
            
            if (pda.toBase58() === CONFIG.TARGET_PDAS.remainingAccount) {
              console.log(`🎯 НАЙДЕН REMAINING ACCOUNT PDA!`);
              console.log(`  Prefix: "${prefix}"`);
              console.log(`  Value: ${value}`);
              console.log(`  Encoding: ${encoding.type}`);
              console.log(`  Bump: ${bump}`);
              console.log(`  Seeds:`);
              seeds.forEach((seed, i) => {
                console.log(`    [${i}] ${this.decodeSeed(seed)}`);
              });
              
              this.foundFormulas.remainingAccount = {
                prefix,
                value,
                encoding: encoding.type,
                bump,
                seeds: seeds.map(s => s.toString('hex'))
              };
              
              return true;
            }
          } catch (e) {
            // Игнорируем ошибки
          }
        }
      }
    }
    
    console.log('❌ Remaining Account PDA не найден');
    return false;
  }

  /**
   * 🔍 ДЕКОДИРОВАНИЕ SEED
   */
  decodeSeed(seed) {
    try {
      const str = seed.toString('utf8');
      if (/^[\x20-\x7E]+$/.test(str)) {
        return `"${str}"`;
      }
    } catch {}
    
    if (seed.length === 2) {
      const num = seed.readInt16LE(0);
      return `i16(${num})`;
    } else if (seed.length === 4) {
      const num = seed.readInt32LE(0);
      return `i32(${num})`;
    } else if (seed.length === 8) {
      const num = seed.readBigInt64LE(0);
      return `i64(${num})`;
    } else if (seed.length === 32) {
      return `PublicKey(${new PublicKey(seed).toBase58()})`;
    }
    
    return `${seed.length}bytes(${seed.toString('hex')})`;
  }

  /**
   * 📊 ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport() {
    console.log('\n📊 ОТЧЕТ О НАЙДЕННЫХ ФОРМУЛАХ:');
    console.log('='.repeat(60));
    
    const foundCount = Object.keys(this.foundFormulas).length;
    console.log(`Найдено формул: ${foundCount}/3`);
    
    if (foundCount > 0) {
      console.log('\n🎯 НАЙДЕННЫЕ ФОРМУЛЫ:');
      
      Object.entries(this.foundFormulas).forEach(([type, formula]) => {
        console.log(`\n${type.toUpperCase()}:`);
        console.log(`  Формула: ${JSON.stringify(formula, null, 2)}`);
      });
      
      // Сохраняем в файл
      const outputDir = './output';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const report = {
        timestamp: new Date().toISOString(),
        targetPDAs: CONFIG.TARGET_PDAS,
        foundFormulas: this.foundFormulas,
        reproductionCode: this.generateReproductionCode()
      };
      
      const filePath = `${outputDir}/meteora-found-formulas.json`;
      fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
      
      console.log(`\n💾 Отчет сохранен: ${filePath}`);
    }
    
    return this.foundFormulas;
  }

  /**
   * 🔄 ГЕНЕРАЦИЯ КОДА ВОСПРОИЗВЕДЕНИЯ
   */
  generateReproductionCode() {
    const code = [];
    
    Object.entries(this.foundFormulas).forEach(([type, formula]) => {
      const seeds = formula.seeds.map(hex => `Buffer.from('${hex}', 'hex')`);
      
      code.push(`
// ${type.toUpperCase()} PDA
const ${type}Seeds = [
  ${seeds.join(',\n  ')}
];
const [${type}PDA, ${type}Bump] = PublicKey.findProgramAddressSync(
  ${type}Seeds, 
  new PublicKey('${CONFIG.METEORA_PROGRAM_ID.toBase58()}')
);
console.log('${type} PDA:', ${type}PDA.toBase58()); // Ожидается: ${CONFIG.TARGET_PDAS[type]}
      `.trim());
    });
    
    return code.join('\n\n');
  }
}

// === MAIN EXECUTION ===
async function main() {
  console.log('🎯 METEORA TARGETED INTERCEPTOR');
  console.log('ЦЕЛЕНАПРАВЛЕННЫЙ ПОИСК ВАШИХ КОНКРЕТНЫХ PDA');
  console.log('='.repeat(60));
  
  const searcher = new TargetedPDASearcher();
  
  try {
    // Ищем каждый PDA
    const positionFound = searcher.searchPositionPDA();
    const bitmapFound = searcher.searchBitmapExtensionPDA();
    const remainingFound = searcher.searchRemainingAccountPDA();
    
    // Генерируем отчет
    const formulas = searcher.generateReport();
    
    const foundCount = Object.keys(formulas).length;
    
    if (foundCount === 3) {
      console.log('\n🎉 ВСЕ PDA НАЙДЕНЫ!');
      console.log('Теперь у вас есть точные формулы для воспроизведения.');
    } else if (foundCount > 0) {
      console.log(`\n⚠️ НАЙДЕНО ${foundCount}/3 PDA`);
      console.log('Продолжайте поиск для оставшихся.');
    } else {
      console.log('\n❌ PDA НЕ НАЙДЕНЫ');
      console.log('Возможно, нужно расширить диапазоны поиска или попробовать другие префиксы.');
    }
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  TargetedPDASearcher,
  CONFIG
};
