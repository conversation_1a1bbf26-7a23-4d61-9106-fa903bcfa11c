/**
 * 🔥 ПОИСК РЕАЛЬНЫХ METEORA ПУЛОВ
 * ИЩЕМ СУЩЕСТВУЮЩИЕ WSOL-USDC ПУЛЫ В METEORA
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function findRealMeteoraPools() {
    console.log('🔥 ПОИСК РЕАЛЬНЫХ METEORA ПУЛОВ...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // Meteora DLMM Program ID
        const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Token Mints
        const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
        const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

        console.log('📊 ПОИСК METEORA LB PAIRS С ФИЛЬТРАМИ...');

        // Попробуем найти LB Pairs с более точными фильтрами
        try {
            console.log('🔍 Попытка 1: Поиск по размеру данных...');
            
            const lbPairs = await connection.getProgramAccounts(METEORA_PROGRAM_ID, {
                filters: [
                    {
                        dataSize: 1000 // Точный размер LB Pair
                    }
                ]
            });
            
            console.log(`   Найдено ${lbPairs.length} аккаунтов размером 1000 байт`);
            
            if (lbPairs.length > 0) {
                console.log('   ✅ Найдены аккаунты! Проверяем первые 5...');
                
                for (let i = 0; i < Math.min(5, lbPairs.length); i++) {
                    const account = lbPairs[i];
                    console.log(`   ${i + 1}. ${account.pubkey.toString()}`);
                    console.log(`      Размер: ${account.account.data.length} байт`);
                    console.log(`      Lamports: ${account.account.lamports}`);
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Ошибка поиска по размеру: ${error.message}`);
        }

        // Попробуем другие размеры
        const sizes = [800, 900, 1000, 1100, 1200, 1300, 1400, 1500];
        
        for (const size of sizes) {
            try {
                console.log(`\n🔍 Поиск аккаунтов размером ${size} байт...`);
                
                const accounts = await connection.getProgramAccounts(METEORA_PROGRAM_ID, {
                    filters: [
                        {
                            dataSize: size
                        }
                    ]
                });
                
                console.log(`   Найдено: ${accounts.length} аккаунтов`);
                
                if (accounts.length > 0 && accounts.length < 100) {
                    console.log(`   ✅ Подходящее количество! Проверяем первые 3...`);
                    
                    for (let i = 0; i < Math.min(3, accounts.length); i++) {
                        const account = accounts[i];
                        console.log(`   ${i + 1}. ${account.pubkey.toString()}`);
                        
                        // Проверяем содержит ли WSOL и USDC
                        const data = account.account.data;
                        const dataHex = data.toString('hex');
                        const wsolHex = WSOL_MINT.toBuffer().toString('hex');
                        const usdcHex = USDC_MINT.toBuffer().toString('hex');
                        
                        const hasWSol = dataHex.includes(wsolHex);
                        const hasUSDC = dataHex.includes(usdcHex);
                        
                        console.log(`      WSOL: ${hasWSol ? '✅' : '❌'}, USDC: ${hasUSDC ? '✅' : '❌'}`);
                        
                        if (hasWSol && hasUSDC) {
                            console.log(`      🎯 НАЙДЕН WSOL-USDC POOL!`);
                            
                            // Проверяем Reserve PDA
                            const [reserveX] = PublicKey.findProgramAddressSync(
                                [
                                    Buffer.from('reserve'),
                                    account.pubkey.toBuffer(),
                                    WSOL_MINT.toBuffer()
                                ],
                                METEORA_PROGRAM_ID
                            );
                            
                            const [reserveY] = PublicKey.findProgramAddressSync(
                                [
                                    Buffer.from('reserve'),
                                    account.pubkey.toBuffer(),
                                    USDC_MINT.toBuffer()
                                ],
                                METEORA_PROGRAM_ID
                            );
                            
                            console.log(`      Reserve X: ${reserveX.toString()}`);
                            console.log(`      Reserve Y: ${reserveY.toString()}`);
                            
                            // Проверяем существование Reserve
                            const reserveXInfo = await connection.getAccountInfo(reserveX);
                            const reserveYInfo = await connection.getAccountInfo(reserveY);
                            
                            console.log(`      Reserve X существует: ${reserveXInfo ? '✅' : '❌'}`);
                            console.log(`      Reserve Y существует: ${reserveYInfo ? '✅' : '❌'}`);
                            
                            if (reserveXInfo && reserveYInfo) {
                                console.log(`\n🎉 НАЙДЕН РАБОЧИЙ WSOL-USDC POOL!`);
                                console.log(`🔥 LB PAIR: ${account.pubkey.toString()}`);
                                console.log(`🔥 RESERVE X: ${reserveX.toString()}`);
                                console.log(`🔥 RESERVE Y: ${reserveY.toString()}`);
                                
                                return {
                                    lbPair: account.pubkey.toString(),
                                    reserveX: reserveX.toString(),
                                    reserveY: reserveY.toString(),
                                    dataSize: size,
                                    valid: true
                                };
                            }
                        }
                    }
                }
                
                // Небольшая задержка между запросами
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.log(`   ❌ Ошибка для размера ${size}: ${error.message}`);
            }
        }

        console.log('\n❌ РАБОЧИЕ WSOL-USDC ПУЛЫ НЕ НАЙДЕНЫ!');
        console.log('🔧 ВОЗМОЖНЫЕ РЕШЕНИЯ:');
        console.log('   1. Создать новый LB Pair через initialize_lb_pair');
        console.log('   2. Найти существующий пул через Meteora UI');
        console.log('   3. Использовать другую DEX (Orca, Raydium)');

        return null;

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск поиска
if (require.main === module) {
    findRealMeteoraPools()
        .then(result => {
            if (result && result.valid) {
                console.log('\n✅ НАЙДЕН РАБОЧИЙ METEORA POOL!');
                process.exit(0);
            } else {
                console.log('\n❌ РАБОЧИЕ METEORA POOLS НЕ НАЙДЕНЫ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { findRealMeteoraPools };
