/**
 * 🔍 METEORA INIT DISCRIMINATOR INTERCEPTOR
 * Перехватывает создание init инструкций для получения discriminator
 */

const { PublicKey, Transaction, TransactionInstruction } = require('@solana/web3.js');

class MeteoraInitDiscriminatorInterceptor {
    constructor() {
        this.capturedDiscriminators = {};
        this.isActive = false;
        this.originalMethods = {};
    }

    /**
     * 🎯 АКТИВАЦИЯ ПЕРЕХВАТЧИКА
     */
    activate() {
        if (this.isActive) return;
        
        console.log('🔍 АКТИВАЦИЯ METEORA INIT DISCRIMINATOR INTERCEPTOR');
        
        // Перехватываем Transaction.add
        this.originalMethods.transactionAdd = Transaction.prototype.add;
        Transaction.prototype.add = (...instructions) => {
            instructions.forEach(ix => {
                this.analyzeInstruction(ix);
            });
            return this.originalMethods.transactionAdd.apply(this, instructions);
        };

        // Перехватываем создание TransactionInstruction
        this.originalMethods.transactionInstruction = TransactionInstruction;
        global.TransactionInstruction = class extends TransactionInstruction {
            constructor(opts) {
                super(opts);
                if (opts && opts.programId && opts.programId.toBase58() === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
                    interceptor.analyzeInstruction(this);
                }
            }
        };

        this.isActive = true;
    }

    /**
     * 🔍 АНАЛИЗ ИНСТРУКЦИИ
     */
    analyzeInstruction(instruction) {
        if (!instruction || !instruction.programId) return;
        
        const programId = instruction.programId.toBase58();
        if (programId !== 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') return;

        const discriminator = instruction.data.slice(0, 8);
        const discriminatorHex = discriminator.toString('hex');
        const discriminatorArray = Array.from(discriminator);

        // Определяем тип инструкции по структуре аккаунтов
        const instructionType = this.identifyInstructionType(instruction);

        console.log(`🔍 METEORA INSTRUCTION INTERCEPTED:`);
        console.log(`   Type: ${instructionType}`);
        console.log(`   Discriminator: ${discriminatorHex}`);
        console.log(`   Array: [${discriminatorArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
        console.log(`   Accounts: ${instruction.keys.length}`);
        console.log(`   Data: ${instruction.data.length} bytes`);

        // Сохраняем discriminator
        this.capturedDiscriminators[discriminatorHex] = {
            type: instructionType,
            discriminator: discriminatorArray,
            hex: discriminatorHex,
            accountsCount: instruction.keys.length,
            dataLength: instruction.data.length,
            accounts: instruction.keys.map((key, index) => ({
                index,
                pubkey: key.pubkey.toBase58(),
                isSigner: key.isSigner,
                isWritable: key.isWritable
            })),
            timestamp: Date.now()
        };

        // Специальная обработка для init инструкций
        if (instructionType.includes('init')) {
            console.log(`🎯 INIT INSTRUCTION FOUND: ${instructionType}`);
            console.log(`   Discriminator для ${instructionType}: [${discriminatorArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
        }
    }

    /**
     * 🎯 ОПРЕДЕЛЕНИЕ ТИПА ИНСТРУКЦИИ
     */
    identifyInstructionType(instruction) {
        const accountsCount = instruction.keys.length;
        const dataLength = instruction.data.length;

        // Анализируем по количеству аккаунтов и размеру данных
        if (accountsCount >= 4 && accountsCount <= 6 && dataLength <= 16) {
            // Возможные init инструкции
            if (accountsCount === 5) {
                return 'possible_init_position';
            } else if (accountsCount === 4) {
                return 'possible_init_bitmap_extension';
            } else if (accountsCount === 6) {
                return 'possible_init_reserve';
            }
            return 'possible_init_unknown';
        } else if (accountsCount >= 14 && accountsCount <= 18) {
            return 'add_liquidity2';
        } else if (accountsCount >= 8 && accountsCount <= 12) {
            return 'possible_remove_liquidity';
        }

        return 'unknown';
    }

    /**
     * 📊 ПОЛУЧЕНИЕ РЕЗУЛЬТАТОВ
     */
    getResults() {
        return {
            discriminators: this.capturedDiscriminators,
            initDiscriminators: Object.entries(this.capturedDiscriminators)
                .filter(([hex, data]) => data.type.includes('init'))
                .reduce((acc, [hex, data]) => {
                    acc[data.type] = {
                        hex,
                        array: data.discriminator,
                        accounts: data.accountsCount
                    };
                    return acc;
                }, {}),
            totalCaptured: Object.keys(this.capturedDiscriminators).length
        };
    }

    /**
     * 💾 СОХРАНЕНИЕ РЕЗУЛЬТАТОВ
     */
    saveResults(filename = 'meteora-init-discriminators.json') {
        const fs = require('fs');
        const results = this.getResults();
        
        fs.writeFileSync(filename, JSON.stringify(results, null, 2));
        console.log(`💾 РЕЗУЛЬТАТЫ СОХРАНЕНЫ В ${filename}`);
        
        return results;
    }

    /**
     * 🔄 ДЕАКТИВАЦИЯ ПЕРЕХВАТЧИКА
     */
    deactivate() {
        if (!this.isActive) return;
        
        console.log('🔄 ДЕАКТИВАЦИЯ METEORA INIT DISCRIMINATOR INTERCEPTOR');
        
        // Восстанавливаем оригинальные методы
        if (this.originalMethods.transactionAdd) {
            Transaction.prototype.add = this.originalMethods.transactionAdd;
        }
        
        if (this.originalMethods.transactionInstruction) {
            global.TransactionInstruction = this.originalMethods.transactionInstruction;
        }

        this.isActive = false;
        
        // Показываем результаты
        const results = this.getResults();
        console.log(`📊 ПЕРЕХВАЧЕНО ${results.totalCaptured} ИНСТРУКЦИЙ`);
        
        if (Object.keys(results.initDiscriminators).length > 0) {
            console.log('🎯 НАЙДЕННЫЕ INIT DISCRIMINATORS:');
            Object.entries(results.initDiscriminators).forEach(([type, data]) => {
                console.log(`   ${type}: [${data.array.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
            });
        }
        
        return results;
    }
}

// Создаем глобальный экземпляр
const interceptor = new MeteoraInitDiscriminatorInterceptor();

// Экспортируем
module.exports = {
    MeteoraInitDiscriminatorInterceptor,
    interceptor,
    
    // Удобные функции
    startCapture: () => interceptor.activate(),
    stopCapture: () => interceptor.deactivate(),
    getResults: () => interceptor.getResults(),
    saveResults: (filename) => interceptor.saveResults(filename)
};

// Автоматическая активация при импорте
if (require.main !== module) {
    console.log('🔍 METEORA INIT DISCRIMINATOR INTERCEPTOR ГОТОВ К РАБОТЕ');
}
