/**
 * 🔍 ПРОВЕРКА ТИПА АККАУНТОВ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
 * ОПРЕДЕЛЯЕМ: ЭТО БИНЫ ИЛИ BIN ARRAYS?
 */

const { Connection, PublicKey } = require('@solana/web3.js');

async function checkAccountType() {
    console.log('🔍 ПРОВЕРЯЕМ ТИПЫ АККАУНТОВ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ...\n');

    // Подключение к RPC
    const connection = new Connection('https://mainnet.helius-rpc.com/?api-key=1b348bc6-3d8a-4b5b-9c4e-8f7a6d5c4b3a');

    // Аккаунты из успешной транзакции
    const accounts = [
        '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF',
        '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4'
    ];

    // Meteora DLMM Program ID
    const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

    for (let i = 0; i < accounts.length; i++) {
        const accountAddress = accounts[i];
        console.log(`🔍 ПРОВЕРЯЕМ АККАУНТ #${i + 1}: ${accountAddress}`);

        try {
            const accountInfo = await connection.getAccountInfo(new PublicKey(accountAddress));

            if (!accountInfo) {
                console.log('   ❌ АККАУНТ НЕ НАЙДЕН!');
                continue;
            }

            console.log(`   ✅ Аккаунт найден`);
            console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
            console.log(`   🏛️ Владелец: ${accountInfo.owner.toString()}`);
            console.log(`   💰 Lamports: ${accountInfo.lamports}`);

            // Проверяем владельца
            const isMeteoraOwned = accountInfo.owner.equals(METEORA_PROGRAM_ID);
            console.log(`   🎯 Принадлежит Meteora: ${isMeteoraOwned ? '✅ ДА' : '❌ НЕТ'}`);

            if (isMeteoraOwned && accountInfo.data.length > 0) {
                // Анализируем структуру данных
                console.log('   🔍 АНАЛИЗ СТРУКТУРЫ ДАННЫХ:');
                
                // Читаем первые байты (discriminator)
                const discriminator = accountInfo.data.slice(0, 8);
                console.log(`   📝 Discriminator: ${discriminator.toString('hex')}`);

                // Проверяем размер для определения типа
                if (accountInfo.data.length > 2000) {
                    console.log('   🎯 ВЕРОЯТНО: BIN ARRAY (большой размер данных)');
                    
                    // Пытаемся прочитать структуру Bin Array
                    try {
                        let offset = 8; // Пропускаем discriminator
                        
                        // Version (1 байт)
                        const version = accountInfo.data.readUInt8(offset);
                        offset += 1;
                        console.log(`   📊 Version: ${version}`);
                        
                        // Index (8 байт, i64)
                        const index = accountInfo.data.readBigInt64LE(offset);
                        offset += 8;
                        console.log(`   📊 Bin Array Index: ${index}`);
                        
                        // Bitmap (16 байт, 2 x u64)
                        const bitmap1 = accountInfo.data.readBigUInt64LE(offset);
                        offset += 8;
                        const bitmap2 = accountInfo.data.readBigUInt64LE(offset);
                        offset += 8;
                        console.log(`   📊 Bitmap: ${bitmap1.toString(16)}, ${bitmap2.toString(16)}`);
                        
                        console.log('   ✅ ПОДТВЕРЖДЕНО: ЭТО BIN ARRAY!');
                        
                    } catch (error) {
                        console.log(`   ⚠️ Ошибка чтения структуры: ${error.message}`);
                    }
                    
                } else if (accountInfo.data.length < 100) {
                    console.log('   🎯 ВЕРОЯТНО: ОТДЕЛЬНЫЙ БИН (маленький размер данных)');
                    
                    // Пытаемся прочитать структуру Bin
                    try {
                        let offset = 8; // Пропускаем discriminator
                        
                        // Читаем возможные поля бина
                        const field1 = accountInfo.data.readBigUInt64LE(offset);
                        offset += 8;
                        const field2 = accountInfo.data.readBigUInt64LE(offset);
                        offset += 8;
                        
                        console.log(`   📊 Field 1: ${field1}`);
                        console.log(`   📊 Field 2: ${field2}`);
                        
                        console.log('   ✅ ПОДТВЕРЖДЕНО: ЭТО ОТДЕЛЬНЫЙ БИН!');
                        
                    } catch (error) {
                        console.log(`   ⚠️ Ошибка чтения структуры: ${error.message}`);
                    }
                    
                } else {
                    console.log('   🤔 НЕОПРЕДЕЛЕННЫЙ ТИП (средний размер данных)');
                }
            }

        } catch (error) {
            console.log(`   💥 ОШИБКА: ${error.message}`);
        }

        console.log(''); // Пустая строка для разделения
    }

    // Дополнительная проверка: пытаемся сгенерировать эти адреса
    console.log('🔧 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: ГЕНЕРАЦИЯ PDA');
    console.log('==========================================');

    const lbPair = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');

    // Проверяем как Bin Arrays
    console.log('🔍 ПРОВЕРКА КАК BIN ARRAYS:');
    for (let index = -70; index <= -60; index++) {
        try {
            const indexBuffer = Buffer.alloc(8);
            indexBuffer.writeBigInt64LE(BigInt(index), 0);
            
            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin_array'),
                lbPair.toBuffer(),
                indexBuffer
            ], METEORA_PROGRAM_ID);

            if (accounts.includes(binArrayPDA.toString())) {
                console.log(`   ✅ НАЙДЕНО СОВПАДЕНИЕ! Index ${index}: ${binArrayPDA.toString()}`);
            }
        } catch (error) {
            // Игнорируем ошибки
        }
    }

    // Проверяем как отдельные бины
    console.log('\n🔍 ПРОВЕРКА КАК ОТДЕЛЬНЫЕ БИНЫ:');
    for (let binId = -4600; binId <= -4500; binId++) {
        try {
            const binIdBuffer = Buffer.alloc(4);
            binIdBuffer.writeInt32LE(binId, 0);
            
            const [binPDA] = PublicKey.findProgramAddressSync([
                Buffer.from('bin'),
                lbPair.toBuffer(),
                binIdBuffer
            ], METEORA_PROGRAM_ID);

            if (accounts.includes(binPDA.toString())) {
                console.log(`   ✅ НАЙДЕНО СОВПАДЕНИЕ! Bin ID ${binId}: ${binPDA.toString()}`);
            }
        } catch (error) {
            // Игнорируем ошибки
        }
    }

    console.log('\n🎯 ПРОВЕРКА ЗАВЕРШЕНА!');
}

// Запуск проверки
if (require.main === module) {
    checkAccountType()
        .then(() => {
            console.log('✅ ПРОВЕРКА ЗАВЕРШЕНА УСПЕШНО!');
            process.exit(0);
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { checkAccountType };
