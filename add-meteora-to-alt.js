/**
 * 🔥 ДОБАВЛЕНИЕ 2 НОВЫХ BIN ARRAY PDA В КАСТОМНУЮ ALT ТАБЛИЦУ
 * РАБОЧИЙ СКРИПТ через JavaScript API (без CLI)
 * ЭКОНОМИЯ: 2 × 32 bytes = 64 bytes (BIN ARRAYS ДЛЯ POOL_1)
 */

const {
    Connection,
    PublicKey,
    Keypair,
    TransactionMessage,
    VersionedTransaction,
    AddressLookupTableProgram
} = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function addMeteoraPositionsToALT() {
    console.log('🔥 ДОБАВЛЕНИЕ 2 НОВЫХ BIN ARRAY PDA В КАСТОМНУЮ ALT ТАБЛИЦУ');
    console.log('🎯 ИСПОЛЬЗУЕМ РАБОЧИЙ JAVASCRIPT API (AddressLookupTableProgram)');
    console.log('💾 ЭКОНОМИЯ: 2 × 32 bytes = 64 bytes (BIN ARRAYS ДЛЯ POOL_1)');
    console.log('⚠️ POSITIONS УЖЕ ДОБАВЛЕНЫ! RESERVE И BITMAP НЕ СУЩЕСТВУЮТ!');
    console.log('=' .repeat(80));

    try {
        // 1. Проверяем переменные окружения
        console.log(`🔍 SOLANA_RPC_URL: ${process.env.SOLANA_RPC_URL ? 'ЗАГРУЖЕН' : 'НЕ НАЙДЕН'}`);

        if (!process.env.SOLANA_RPC_URL) {
            throw new Error('❌ SOLANA_RPC_URL не найден в .env.solana');
        }

        // 2. Подключение к RPC для проверки (ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ SOLANA RPC)
        const connection = new Connection(process.env.SOLANA_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 3. Загружаем кошелек для подписи транзакций
        let wallet;
        if (fs.existsSync('wallet.json')) {
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
            console.log(`✅ Кошелек загружен: ${wallet.publicKey.toString()}`);
        } else {
            throw new Error('Файл кошелька wallet.json не найден!');
        }

        // 3. ДЕНЬГИ ЕСТЬ - РАБОТАЕМ БЕЗ ПРОВЕРОК!
        console.log(`💰 ДЕНЬГИ ЕСТЬ - ПРОПУСКАЕМ ПРОВЕРКУ БАЛАНСА!`);

        // 4. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        // 🔥 ДОБАВЛЯЕМ ТОЛЬКО 2 НОВЫХ BIN ARRAY PDA (СУЩЕСТВУЮТ В БЛОКЧЕЙНЕ!)
        const meteoraAddresses = [
            // ✅ НОВЫЕ BIN ARRAY PDA ДЛЯ POOL_1 (ПРОВЕРЕНЫ В БЛОКЧЕЙНЕ!)
            'BprFUVy4S1VPewTovUDYgxsCnkWmwAFg6SLNBPrtm8sK', // POOL_1_BIN_ARRAY_-66 ✅
            '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // POOL_1_BIN_ARRAY_-65 ✅
            // ❌ POSITIONS УЖЕ ДОБАВЛЕНЫ В ПРЕДЫДУЩЕЙ ТРАНЗАКЦИИ!
            // ❌ RESERVE И BITMAP PDA НЕ СУЩЕСТВУЮТ В БЛОКЧЕЙНЕ!
        ];
        console.log(`🔑 ДОБАВЛЯЕМ 2 НОВЫХ BIN ARRAY PDA (СУЩЕСТВУЮТ В БЛОКЧЕЙНЕ):`);
        const names = [
            'POOL_1 Bin Array -66 (ПРОВЕРЕН ✅)',
            'POOL_1 Bin Array -65 (ПРОВЕРЕН ✅)',
        ];
        meteoraAddresses.forEach((key, i) => {
            console.log(`   ${i + 1}. ${key} (${names[i]})`);
        });

        // ДОБАВЛЯЕМ 2 POSITION АККАУНТА ИЗ НАШЕЙ ТРАНЗАКЦИИ
        const addressesToAdd = meteoraAddresses;

        console.log(`📊 Адресов для добавления: ${addressesToAdd.length}`);
        console.log(`🎯 Экономия: ${addressesToAdd.length} × 32 bytes = ${addressesToAdd.length * 32} bytes`);

        // 6. Проверяем текущее состояние ALT таблицы
        console.log('\n🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ ALT ТАБЛИЦЫ...');
        const altAccount = await connection.getAddressLookupTable(customALTAddress);

        if (!altAccount || !altAccount.value) {
            throw new Error('Кастомная ALT таблица не найдена в блокчейне!');
        }

        const currentAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Текущих адресов в ALT: ${currentAddresses.length}`);

        // 6. ДОБАВЛЯЕМ ВСЕ АДРЕСА БЕЗ ПРОВЕРОК И ОГРАНИЧЕНИЙ!
        const newAddresses = addressesToAdd; // БЕЗ ФИЛЬТРАЦИИ!
        console.log(`🔥 ДОБАВЛЯЕМ ВСЕ ${newAddresses.length} АДРЕСОВ БЕЗ ПРОВЕРОК!`);

        // 8. Показываем адреса которые будем добавлять
        console.log(`\n📋 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ (${newAddresses.length}):`);
        newAddresses.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr}`);
        });

        // 9. Создаем инструкцию для расширения ALT таблицы
        console.log(`\n🔧 СОЗДАНИЕ ИНСТРУКЦИИ ДЛЯ ДОБАВЛЕНИЯ ${newAddresses.length} АДРЕСОВ...`);

        // Валидируем и конвертируем строки в PublicKey
        const addressPublicKeys = [];
        for (const addr of newAddresses) {
            try {
                const pubkey = new PublicKey(addr);
                addressPublicKeys.push(pubkey);
                console.log(`✅ Валидный адрес: ${addr.slice(0,8)}...${addr.slice(-8)}`);
            } catch (error) {
                console.log(`❌ ПРОПУСКАЕМ невалидный адрес: ${addr} - ${error.message}`);
            }
        }

        console.log(`✅ Валидных адресов: ${addressPublicKeys.length} из ${newAddresses.length}`);

        // Создаем инструкцию extend
        const extendInstruction = AddressLookupTableProgram.extendLookupTable({
            payer: wallet.publicKey,
            authority: wallet.publicKey,
            lookupTable: customALTAddress,
            addresses: addressPublicKeys
        });

        console.log('✅ Инструкция extend создана');

        // 10. Создаем и отправляем транзакцию
        console.log('\n🚀 СОЗДАНИЕ И ОТПРАВКА ТРАНЗАКЦИИ...');

        // Получаем последний blockhash
        const { blockhash } = await connection.getLatestBlockhash('confirmed');
        console.log(`✅ Blockhash получен: ${blockhash.slice(0, 8)}...`);

        // Создаем транзакцию
        const message = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [extendInstruction]
        }).compileToV0Message();

        const transaction = new VersionedTransaction(message);

        // Подписываем транзакцию
        transaction.sign([wallet]);
        console.log('✅ Транзакция подписана');

        // Отправляем транзакцию БЕЗ PREFLIGHT
        const signature = await connection.sendTransaction(transaction, {
            maxRetries: 5,
            preflightCommitment: 'confirmed',
            skipPreflight: true
        });

        console.log(`🚀 Транзакция отправлена: ${signature}`);

        // 11. Ждем подтверждения
        console.log('⏳ Ожидание подтверждения транзакции...');

        const confirmation = await connection.confirmTransaction({
            signature,
            blockhash,
            lastValidBlockHeight: (await connection.getLatestBlockhash()).lastValidBlockHeight
        }, 'confirmed');

        if (confirmation.value.err) {
            throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
        }

        console.log('✅ Транзакция подтверждена!');

        // 12. Проверяем результат
        console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА...');

        // Ждем дольше для обновления состояния (блокчейн может быть медленным)
        console.log('⏳ Ожидание обновления ALT таблицы (10 секунд)...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        let updatedAddresses = currentAddresses;
        let attempts = 0;
        const maxAttempts = 5;

        // Пытаемся несколько раз получить обновленное состояние
        while (attempts < maxAttempts) {
            try {
                const updatedAltAccount = await connection.getAddressLookupTable(customALTAddress);
                if (updatedAltAccount && updatedAltAccount.value) {
                    updatedAddresses = updatedAltAccount.value.state.addresses.map(addr => addr.toString());

                    if (updatedAddresses.length > currentAddresses.length) {
                        console.log(`✅ ALT таблица обновлена! Попытка ${attempts + 1}`);
                        break;
                    }
                }

                attempts++;
                if (attempts < maxAttempts) {
                    console.log(`⏳ Попытка ${attempts}: ALT еще не обновлена, ждем еще 5 секунд...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                }
            } catch (error) {
                console.log(`⚠️ Ошибка при проверке ALT (попытка ${attempts + 1}): ${error.message}`);
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        console.log(`📊 Адресов в ALT до обновления: ${currentAddresses.length}`);
        console.log(`📊 Адресов в ALT после обновления: ${updatedAddresses.length}`);
        console.log(`✅ Добавлено новых адресов: ${updatedAddresses.length - currentAddresses.length}`);

        // 13. Сохраняем результат
        const result = {
            timestamp: new Date().toISOString(),
            method: 'javascript-api',
            transactionSignature: signature,
            altTableAddress: customALTAddress.toString(),
            addressesBeforeUpdate: currentAddresses.length,
            addressesAfterUpdate: updatedAddresses.length,
            addressesAdded: updatedAddresses.length - currentAddresses.length,
            newAddressesAdded: newAddresses,
            success: true
        };

        const resultFile = 'alt-update-result.json';
        fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
        console.log(`✅ Результат сохранен в: ${resultFile}`);

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎉 2 ПРОВЕРЕННЫХ АККАУНТА УСПЕШНО ДОБАВЛЕНЫ В ALT ТАБЛИЦУ!');
        console.log(`📊 Метод: JavaScript API (AddressLookupTableProgram)`);
        console.log(`📊 Транзакция: ${signature}`);
        console.log(`📊 Добавлено адресов: ${updatedAddresses.length - currentAddresses.length}`);
        console.log(`📊 Всего адресов в ALT: ${updatedAddresses.length}`);
        console.log(`💾 Экономия: ${updatedAddresses.length - currentAddresses.length} × 32 bytes = ${(updatedAddresses.length - currentAddresses.length) * 32} bytes`);
        console.log(`🔥 РАЗМЕР ТРАНЗАКЦИИ УМЕНЬШИТСЯ НА ~64 БАЙТА!`);
        console.log(`⚠️ RESERVE АККАУНТЫ НЕ ДОБАВЛЕНЫ - НЕ СУЩЕСТВУЮТ В БЛОКЧЕЙНЕ!`);
        console.log(`${'='.repeat(80)}`);

        // 🔥 НОВАЯ ФУНКЦИЯ: СКАЧИВАЕМ ОБНОВЛЕННУЮ ТАБЛИЦУ И ДОБАВЛЯЕМ В custom-alt-data
        await updateCustomAltData(connection, customALTAddress);

    } catch (error) {
        console.error('❌ Ошибка добавления адресов:', error.message);
        console.error(error.stack);

        // Сохраняем ошибку
        const errorResult = {
            timestamp: new Date().toISOString(),
            method: 'javascript-api',
            error: error.message,
            stack: error.stack,
            success: false
        };

        fs.writeFileSync('alt-update-error.json', JSON.stringify(errorResult, null, 2));

        console.log('\n💡 ВОЗМОЖНЫЕ РЕШЕНИЯ:');
        console.log('1. Проверьте права доступа к ALT таблице (authority)');
        console.log('2. Попробуйте добавить адреса по частям (меньшими группами)');
        console.log('3. Проверьте что кошелек является authority для ALT таблицы');
    }
}

// Запуск добавления
if (require.main === module) {
    addMeteoraPositionsToALT();
}

/**
 * 🔥 НОВАЯ ФУНКЦИЯ: СКАЧИВАНИЕ ОБНОВЛЕННОЙ ALT ТАБЛИЦЫ И ДОБАВЛЕНИЕ В custom-alt-data
 * НЕ ТРОГАЕТ СТРУКТУРУ И НЕ ПОВРЕЖДАЕТ MARGINFI ТАБЛИЦУ!
 */
async function updateCustomAltData(connection, customALTAddress) {
    console.log('\n🔥🔥🔥 ОБНОВЛЕНИЕ custom-alt-data ИЗ БЛОКЧЕЙНА 🔥🔥🔥');
    console.log('📊 СКАЧИВАЕМ СВЕЖУЮ ТАБЛИЦУ И ДОБАВЛЯЕМ НЕДОСТАЮЩИЕ АДРЕСА');
    console.log('🛡️ НЕ ТРОГАЕМ MARGINFI ТАБЛИЦУ И СТРУКТУРУ!');
    console.log('=' .repeat(80));

    try {
        // 1. Скачиваем свежую ALT таблицу из блокчейна
        console.log('📥 СКАЧИВАЕМ СВЕЖУЮ ALT ТАБЛИЦУ ИЗ БЛОКЧЕЙНА...');
        const altAccount = await connection.getAddressLookupTable(customALTAddress);

        if (!altAccount || !altAccount.value) {
            throw new Error('❌ Кастомная ALT таблица не найдена в блокчейне!');
        }

        const blockchainAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`✅ Скачано ${blockchainAddresses.length} адресов из блокчейна`);

        // 2. Загружаем текущий custom-alt-data.json
        const customAltDataPath = 'custom-alt-data.json';
        let customAltData = {};

        if (fs.existsSync(customAltDataPath)) {
            customAltData = JSON.parse(fs.readFileSync(customAltDataPath, 'utf8'));
            console.log(`✅ Загружен существующий ${customAltDataPath}`);
        } else {
            console.log(`⚠️ Файл ${customAltDataPath} не найден, создаем новый`);
        }

        // 3. Сохраняем структуру и не трогаем MarginFi данные
        const originalMarginFiData = customAltData.marginfi || {};
        console.log(`🛡️ СОХРАНЯЕМ MARGINFI ДАННЫЕ: ${Object.keys(originalMarginFiData).length} ключей`);

        // 4. Обновляем только custom секцию
        if (!customAltData.custom) {
            customAltData.custom = {};
        }

        // Сохраняем старые адреса custom секции
        const oldCustomAddresses = customAltData.custom.addresses || [];
        console.log(`📊 Старых custom адресов: ${oldCustomAddresses.length}`);

        // 5. Находим новые адреса которых нет в старом списке
        const newAddresses = blockchainAddresses.filter(addr => !oldCustomAddresses.includes(addr));
        console.log(`🆕 Найдено новых адресов: ${newAddresses.length}`);

        if (newAddresses.length > 0) {
            console.log(`📋 НОВЫЕ АДРЕСА:`);
            newAddresses.forEach((addr, i) => {
                console.log(`   ${i + 1}. ${addr.slice(0,8)}...${addr.slice(-8)}`);
            });
        }

        // 6. Обновляем custom секцию с новыми адресами
        customAltData.custom = {
            ...customAltData.custom,
            addresses: blockchainAddresses, // ВСЕ адреса из блокчейна
            lastUpdated: new Date().toISOString(),
            totalAddresses: blockchainAddresses.length,
            newAddressesAdded: newAddresses.length,
            altTableAddress: customALTAddress.toString()
        };

        // 7. ВАЖНО: Восстанавливаем MarginFi данные
        customAltData.marginfi = originalMarginFiData;

        // 8. Сохраняем обновленный файл
        fs.writeFileSync(customAltDataPath, JSON.stringify(customAltData, null, 2));
        console.log(`✅ Обновлен ${customAltDataPath}`);

        // 9. Статистика
        console.log(`\n📊 РЕЗУЛЬТАТ ОБНОВЛЕНИЯ custom-alt-data:`);
        console.log(`   ✅ MarginFi данные: СОХРАНЕНЫ (${Object.keys(originalMarginFiData).length} ключей)`);
        console.log(`   ✅ Custom адресов: ${blockchainAddresses.length} (было ${oldCustomAddresses.length})`);
        console.log(`   🆕 Добавлено новых: ${newAddresses.length}`);
        console.log(`   📅 Обновлено: ${new Date().toLocaleString()}`);

        console.log(`\n🎉 custom-alt-data УСПЕШНО ОБНОВЛЕН БЕЗ ПОВРЕЖДЕНИЯ СТРУКТУРЫ!`);

    } catch (error) {
        console.error(`❌ Ошибка обновления custom-alt-data: ${error.message}`);
        console.error(error.stack);
    }
}

module.exports = { addMeteoraPositionsToALT, updateCustomAltData };
