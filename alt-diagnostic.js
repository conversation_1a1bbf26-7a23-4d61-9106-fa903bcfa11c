/**
 * 🔥 ДИАГНОСТИКА ALT ТАБЛИЦ - КАКИЕ АДРЕСА МОЖНО ДОБАВИТЬ
 */

const { PublicKey, Connection } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

// 🔥 КОНСТАНТЫ
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const POOL_1 = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
const POOL_2 = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
const BIN_ID_1 = -4539; // Активный бин Pool 1
const BIN_ID_2 = -1816; // Активный бин Pool 2

// 🔥 RPC CONNECTION
const connection = new Connection(process.env.QUICKNODE_RPC_URL || 'https://api.mainnet-beta.solana.com');

/**
 * 🔥 ГЕНЕРИРУЕМ ВСЕ ВОЗМОЖНЫЕ PDA ДЛЯ ДОБАВЛЕНИЯ В ALT
 */
function generateAllPDAs() {
    console.log('🔥 ГЕНЕРИРУЕМ ВСЕ ВОЗМОЖНЫЕ PDA ДЛЯ ALT ТАБЛИЦ:');
    console.log('');

    const pdas = [];

    // 🔥 1. BIN_LIQUIDITY PDA (для обоих пулов)
    console.log('📊 1. BIN_LIQUIDITY PDA:');
    
    [POOL_1, POOL_2].forEach((pool, poolIndex) => {
        const binId = poolIndex === 0 ? BIN_ID_1 : BIN_ID_2;
        const binIdBuffer = Buffer.alloc(4);
        binIdBuffer.writeInt32LE(binId, 0);

        const [binLiquidityPDA] = PublicKey.findProgramAddressSync(
            [Buffer.from("bin_liquidity"), pool.toBuffer(), binIdBuffer],
            METEORA_PROGRAM_ID
        );

        pdas.push({
            name: `bin_liquidity_pool_${poolIndex + 1}`,
            address: binLiquidityPDA,
            pool: pool.toString().slice(0, 8),
            binId: binId,
            category: 'bin_liquidity'
        });

        console.log(`   Pool ${poolIndex + 1} (bin ${binId}): ${binLiquidityPDA.toString()}`);
    });

    // 🔥 2. BIN_RESERVE PDA (для обоих пулов)
    console.log('');
    console.log('📊 2. BIN_RESERVE PDA:');
    
    [POOL_1, POOL_2].forEach((pool, poolIndex) => {
        const binId = poolIndex === 0 ? BIN_ID_1 : BIN_ID_2;
        const binIdBuffer = Buffer.alloc(4);
        binIdBuffer.writeInt32LE(binId, 0);

        const [binReservePDA] = PublicKey.findProgramAddressSync(
            [Buffer.from("bin_reserve"), pool.toBuffer(), binIdBuffer],
            METEORA_PROGRAM_ID
        );

        pdas.push({
            name: `bin_reserve_pool_${poolIndex + 1}`,
            address: binReservePDA,
            pool: pool.toString().slice(0, 8),
            binId: binId,
            category: 'bin_reserve'
        });

        console.log(`   Pool ${poolIndex + 1} (bin ${binId}): ${binReservePDA.toString()}`);
    });

    // 🔥 3. BIN_ARRAY PDA (для обоих пулов)
    console.log('');
    console.log('📊 3. BIN_ARRAY PDA:');
    
    [POOL_1, POOL_2].forEach((pool, poolIndex) => {
        const binId = poolIndex === 0 ? BIN_ID_1 : BIN_ID_2;
        const binArrayIndex = Math.floor(binId / 64);
        const binArrayIndexBuffer = Buffer.alloc(8);
        binArrayIndexBuffer.writeBigInt64LE(BigInt(binArrayIndex), 0);

        const [binArrayPDA] = PublicKey.findProgramAddressSync(
            [Buffer.from("bin_array"), pool.toBuffer(), binArrayIndexBuffer],
            METEORA_PROGRAM_ID
        );

        pdas.push({
            name: `bin_array_pool_${poolIndex + 1}`,
            address: binArrayPDA,
            pool: pool.toString().slice(0, 8),
            binId: binId,
            binArrayIndex: binArrayIndex,
            category: 'bin_array'
        });

        console.log(`   Pool ${poolIndex + 1} (bin ${binId}, chunk ${binArrayIndex}): ${binArrayPDA.toString()}`);
    });

    // 🔥 4. СТАТИЧЕСКИЕ АДРЕСА (уже в ALT?)
    console.log('');
    console.log('📊 4. СТАТИЧЕСКИЕ АДРЕСА:');
    
    const staticAddresses = [
        { name: 'METEORA_PROGRAM', address: METEORA_PROGRAM_ID, category: 'program' },
        { name: 'POOL_1', address: POOL_1, category: 'pool' },
        { name: 'POOL_2', address: POOL_2, category: 'pool' },
        { name: 'USDC_MINT', address: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), category: 'mint' },
        { name: 'WSOL_MINT', address: new PublicKey('So11111111111111111111111111111111111111112'), category: 'mint' },
        { name: 'TOKEN_PROGRAM', address: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), category: 'program' },
        { name: 'EVENT_AUTHORITY', address: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), category: 'authority' }
    ];

    staticAddresses.forEach(item => {
        pdas.push(item);
        console.log(`   ${item.name}: ${item.address.toString()}`);
    });

    return pdas;
}

/**
 * 🔥 ПРОВЕРЯЕМ СУЩЕСТВУЮЩИЕ ALT ТАБЛИЦЫ
 */
async function checkExistingALTTables() {
    console.log('');
    console.log('🔥 ПРОВЕРЯЕМ СУЩЕСТВУЮЩИЕ ALT ТАБЛИЦЫ:');
    
    const altAddresses = [
        'HGmknUTUQdpuaKqYrjzZjZAyKdVLBLSLZLLNJhJhJhJh', // MarginFi ALT
        'CustomALTAddressHere123456789012345678901234'      // Custom ALT
    ];

    for (const altAddress of altAddresses) {
        try {
            console.log(`📊 Проверяем ALT: ${altAddress}`);
            
            // Здесь можно добавить проверку через RPC
            // const altInfo = await connection.getAccountInfo(new PublicKey(altAddress));
            
            console.log(`   ⚠️ ALT адрес нужно проверить вручную`);
        } catch (error) {
            console.log(`   ❌ Ошибка проверки ALT: ${error.message}`);
        }
    }
}

/**
 * 🔥 АНАЛИЗИРУЕМ ЭКОНОМИЮ БАЙТ
 */
function analyzeBytesSavings(pdas) {
    console.log('');
    console.log('🔥 АНАЛИЗ ЭКОНОМИИ БАЙТ ПРИ ДОБАВЛЕНИИ В ALT:');
    console.log('');

    const categories = {};
    pdas.forEach(pda => {
        if (!categories[pda.category]) {
            categories[pda.category] = [];
        }
        categories[pda.category].push(pda);
    });

    let totalSavings = 0;

    Object.keys(categories).forEach(category => {
        const items = categories[category];
        const savings = items.length * 31; // 32 байта → 1 байт индекс
        totalSavings += savings;

        console.log(`📊 ${category.toUpperCase()}:`);
        console.log(`   Количество: ${items.length}`);
        console.log(`   Экономия: ${savings} байт (${items.length} × 31)`);
        
        items.forEach(item => {
            console.log(`   - ${item.name}: ${item.address.toString().slice(0, 8)}...`);
        });
        console.log('');
    });

    console.log(`🎯 ОБЩАЯ ЭКОНОМИЯ: ${totalSavings} байт`);
    console.log(`🎯 ЭКОНОМИЯ remaining_accounts_info: ${76} байт → ${2} байт (2 индекса)`);
    console.log(`🎯 ИТОГО ЭКОНОМИЯ: ${totalSavings + 74} байт`);
}

/**
 * 🔥 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    try {
        console.log('🔥 ALT ДИАГНОСТИКА - АНАЛИЗ ВОЗМОЖНЫХ АДРЕСОВ ДЛЯ ТАБЛИЦ');
        console.log('='.repeat(60));
        
        const pdas = generateAllPDAs();
        await checkExistingALTTables();
        analyzeBytesSavings(pdas);
        
        console.log('');
        console.log('🎯 РЕКОМЕНДАЦИИ:');
        console.log('1. Добавить bin_liquidity и bin_reserve PDA в ALT');
        console.log('2. Добавить bin_array PDA в ALT');
        console.log('3. Проверить статические адреса в существующих ALT');
        console.log('4. Экономия: ~150+ байт на транзакцию');
        
    } catch (error) {
        console.error('❌ ОШИБКА ДИАГНОСТИКИ:', error);
    }
}

main();
