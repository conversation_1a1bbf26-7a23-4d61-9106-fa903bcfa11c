{"timestamp": "2025-08-02T05:57:36.419Z", "targetPDAs": {"position": "Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC", "bitmapExtension": "4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH", "remainingAccount": "Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw"}, "foundFormulas": {"remainingAccount": {"prefix": "bin_array", "value": -71, "encoding": "i64", "bump": 254, "seeds": ["62696e5f6172726179", "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "b9ffffffffffffff"]}}, "reproductionCode": "// REMAININGACCOUNT PDA\nconst remainingAccountSeeds = [\n  Buffer.from('62696e5f6172726179', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('b9ffffffffffffff', 'hex')\n];\nconst [remainingAccountPDA, remainingAccountBump] = PublicKey.findProgramAddressSync(\n  remainingAccountSeeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\nconsole.log('remainingAccount PDA:', remainingAccountPDA.toBase58()); // Ожидается: Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw"}