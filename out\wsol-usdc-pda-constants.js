/**
 * 🎯 WSOL-USDC PDA КОНСТАНТЫ ДЛЯ ИНТЕГРАЦИИ
 * Сгенерировано: 2025-08-02T04:57:20.339Z
 * Position совпадает: false
 * Bitmap Extension совпадает: false
 */

const { PublicKey } = require('@solana/web3.js');

// 🔧 ОСНОВНЫЕ КОНСТАНТЫ
const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const WSOL_USDC_LB_PAIR = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
const USER_PUBLIC_KEY = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');

// 🎯 WSOL-USDC PDA АДРЕСА (КАНОНИЧНЫЕ)
const WSOL_USDC_PDA = {
    // Основные PDA для add_liquidity2
    POSITION: new PublicKey('84tqwsC9XxBEHWpreC96gryUGQS6VifAWv2wHs6uY4mt'),
    BITMAP_EXTENSION: new PublicKey('7jeJbbSDpnY9P6Zf8aYeausDoUVpDaJtptYjH1q6dHsB'),
    EVENT_AUTHORITY: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'),
    
    // Дополнительные PDA
    STRATEGY: new PublicKey('4rAF3krhFfQSr8RK3dzmZ3rmX6p4YA64Zu7HHpE3Hync'),
    LIQUIDITY_ACCOUNT: new PublicKey('8XLxY3CnutemjwHYQ4nEwuk326tC5PzUFgY2irrmiGQK'),
    GLOBAL_STATE: new PublicKey('5w4p5tmQprtrpQRen8gugEu9BrvxPu85Vpvya1uLLDeN'),
    
    // Метаданные
    BITMAP_BIN_ID: -4517,
    POSITION_BUMP: 254,
    BITMAP_BUMP: 253,
    EVENT_AUTHORITY_BUMP: 255
};

// 🗑️ BITMAP EXTENSION ФОРМУЛА УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО ЦЕНТРАЛИЗОВАННУЮ ИЗ complete-flash-loan-structure.js!

// 🗑️ POSITION PDA ФОРМУЛА УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО СТАТИЧЕСКИЕ АДРЕСА ИЗ TRADING-CONFIG!

module.exports = {
    METEORA_DLMM_PROGRAM,
    WSOL_USDC_LB_PAIR,
    USER_PUBLIC_KEY,
    WSOL_USDC_PDA
    // 🗑️ getBinArrayBitmapExtensionPDA УДАЛЕН - ИСПОЛЬЗУЕМ ТОЛЬКО ЦЕНТРАЛИЗОВАННУЮ ФОРМУЛУ!
};
