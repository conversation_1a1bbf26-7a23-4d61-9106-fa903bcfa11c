use anyhow::Result;
use lettre::{Message, SmtpTransport, Transport};
use lettre::transport::smtp::authentication::Credentials;
use reqwest::Client;
use serde_json::json;
use log::{info, error};

use crate::config::{Config, BugBountyProgram};
use crate::database::Exploit;

pub struct BugReporter {
    config: Config,
    http_client: Client,
}

impl BugReporter {
    pub async fn new(config: Config) -> Result<Self> {
        Ok(Self {
            config,
            http_client: Client::new(),
        })
    }
    
    pub async fn submit_exploit(&self, exploit: &Exploit) -> Result<String> {
        info!("📤 Submitting exploit: {} (${} potential)", exploit.title, exploit.estimated_reward);
        
        // Находим подходящую bug bounty программу
        let suitable_program = self.find_suitable_program(exploit)?;
        
        match suitable_program.submission_format.as_str() {
            "email" => {
                self.submit_via_email(exploit, &suitable_program).await
            },
            "api" => {
                self.submit_via_api(exploit, &suitable_program).await
            },
            _ => {
                error!("Unknown submission format: {}", suitable_program.submission_format);
                Err(anyhow::anyhow!("Unknown submission format"))
            }
        }
    }
    
    fn find_suitable_program(&self, exploit: &Exploit) -> Result<&BugBountyProgram> {
        // Ищем программу с подходящим максимальным вознаграждением
        let suitable = self.config.bug_bounty_programs
            .iter()
            .filter(|program| program.max_reward >= exploit.estimated_reward)
            .max_by_key(|program| program.max_reward);
        
        suitable.ok_or_else(|| anyhow::anyhow!("No suitable bug bounty program found"))
    }
    
    async fn submit_via_email(&self, exploit: &Exploit, program: &BugBountyProgram) -> Result<String> {
        let email_address = program.email.as_ref()
            .ok_or_else(|| anyhow::anyhow!("No email address for program"))?;
        
        let email_body = self.create_email_body(exploit, program);
        
        let email = Message::builder()
            .from(self.config.email_config.from_address.parse()?)
            .to(email_address.parse()?)
            .subject(format!("CRITICAL VULNERABILITY REPORT - ${} Potential Reward", exploit.estimated_reward))
            .body(email_body)?;
        
        let creds = Credentials::new(
            self.config.email_config.username.clone(),
            self.config.email_config.password.clone(),
        );
        
        let mailer = SmtpTransport::relay(&self.config.email_config.smtp_server)?
            .credentials(creds)
            .build();
        
        match mailer.send(&email) {
            Ok(_) => {
                info!("✅ Email sent successfully to {}", program.name);
                Ok(format!("email-{}-{}", program.name, chrono::Utc::now().timestamp()))
            },
            Err(e) => {
                error!("❌ Failed to send email to {}: {}", program.name, e);
                Err(anyhow::anyhow!("Failed to send email: {}", e))
            }
        }
    }
    
    async fn submit_via_api(&self, exploit: &Exploit, program: &BugBountyProgram) -> Result<String> {
        let api_endpoint = program.api_endpoint.as_ref()
            .ok_or_else(|| anyhow::anyhow!("No API endpoint for program"))?;
        
        let api_key = program.api_key.as_ref()
            .ok_or_else(|| anyhow::anyhow!("No API key for program"))?;
        
        let submission_data = json!({
            "title": exploit.title,
            "description": exploit.description,
            "severity": self.determine_severity(exploit.estimated_reward),
            "vulnerability_type": self.extract_vulnerability_type(&exploit.title),
            "proof_of_concept": exploit.proof_of_concept,
            "estimated_reward": exploit.estimated_reward,
            "program_id": self.extract_program_id(&exploit.title),
            "submission_date": exploit.created_at.to_rfc3339(),
        });
        
        let response = self.http_client
            .post(api_endpoint)
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&submission_data)
            .send()
            .await?;
        
        if response.status().is_success() {
            let response_body: serde_json::Value = response.json().await?;
            let submission_id = response_body["id"]
                .as_str()
                .unwrap_or("unknown")
                .to_string();
            
            info!("✅ API submission successful to {}: {}", program.name, submission_id);
            Ok(submission_id)
        } else {
            let error_text = response.text().await?;
            error!("❌ API submission failed to {}: {}", program.name, error_text);
            Err(anyhow::anyhow!("API submission failed: {}", error_text))
        }
    }
    
    fn create_email_body(&self, exploit: &Exploit, program: &BugBountyProgram) -> String {
        format!(r#"
Subject: CRITICAL VULNERABILITY REPORT - ${} Potential Reward

Dear {} Security Team,

I am reporting a critical vulnerability with an estimated reward potential of ${}.

VULNERABILITY SUMMARY:
- Title: {}
- Affected Program: {}
- Severity: {}
- Estimated Financial Impact: ${}

DETAILED DESCRIPTION:
{}

PROOF OF CONCEPT:
{}

IMPACT ANALYSIS:
This vulnerability poses significant security risks and could result in:
- Financial losses for users and protocols
- Compromise of smart contract integrity
- Potential exploitation by malicious actors
- Damage to ecosystem reputation

TIMELINE:
- Discovered: {}
- Reported: {}

I have prepared a complete proof-of-concept that demonstrates this vulnerability in a controlled environment. The exploit has been tested and verified to work as described.

RESPONSIBLE DISCLOSURE:
I am committed to responsible disclosure and will:
- Keep this vulnerability confidential until patched
- Provide additional technical details if needed
- Assist with testing and verification
- Coordinate public disclosure timeline

CONTACT INFORMATION:
Please confirm receipt of this report and provide a timeline for assessment. I am available for any clarification or additional testing required.

REWARD INFORMATION:
Based on the severity and potential impact, I believe this vulnerability qualifies for a reward in the range of ${} according to your bug bounty program guidelines.

Thank you for your attention to this critical security matter.

Best regards,
Security Researcher

---
This report was generated by an automated vulnerability discovery system.
All findings have been verified and tested in controlled environments.
"#,
            exploit.estimated_reward,
            program.name,
            exploit.estimated_reward,
            exploit.title,
            self.extract_program_id(&exploit.title),
            self.determine_severity(exploit.estimated_reward),
            exploit.estimated_reward,
            exploit.description,
            exploit.proof_of_concept,
            exploit.created_at.format("%Y-%m-%d %H:%M:%S UTC"),
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
            exploit.estimated_reward
        )
    }
    
    fn determine_severity(&self, estimated_reward: u64) -> &'static str {
        match estimated_reward {
            1_000_000.. => "Critical",
            500_000..=999_999 => "High",
            100_000..=499_999 => "Medium",
            _ => "Low",
        }
    }
    
    fn extract_vulnerability_type(&self, title: &str) -> String {
        if title.contains("Reentrancy") {
            "Reentrancy".to_string()
        } else if title.contains("Integer") || title.contains("Overflow") {
            "Integer Overflow".to_string()
        } else if title.contains("Validation") || title.contains("Bypass") {
            "Validation Bypass".to_string()
        } else if title.contains("Memory") || title.contains("Corruption") {
            "Memory Corruption".to_string()
        } else {
            "Logic Error".to_string()
        }
    }
    
    fn extract_program_id(&self, title: &str) -> String {
        // Простое извлечение program ID из заголовка
        if let Some(start) = title.find(" in ") {
            title[start + 4..].to_string()
        } else {
            "Unknown Program".to_string()
        }
    }
    
    pub async fn check_submission_status(&self, submission_id: &str) -> Result<SubmissionStatus> {
        // Заглушка для проверки статуса отправки
        // В реальной реализации здесь был бы запрос к API bug bounty платформы
        
        info!("🔍 Checking status for submission: {}", submission_id);
        
        // Симулируем различные статусы
        let status = if submission_id.contains("email") {
            SubmissionStatus::Pending
        } else if submission_id.starts_with("confirmed") {
            SubmissionStatus::Confirmed
        } else if submission_id.starts_with("rejected") {
            SubmissionStatus::Rejected
        } else {
            SubmissionStatus::UnderReview
        };
        
        Ok(status)
    }
    
    pub async fn monitor_all_submissions(&self, submission_ids: &[String]) -> Result<StatusReport> {
        let mut confirmed_rewards = 0u64;
        let mut pending_rewards = 0u64;
        let mut rejected_rewards = 0u64;
        
        for submission_id in submission_ids {
            match self.check_submission_status(submission_id).await? {
                SubmissionStatus::Confirmed => confirmed_rewards += 100_000, // Примерная награда
                SubmissionStatus::Pending | SubmissionStatus::UnderReview => pending_rewards += 100_000,
                SubmissionStatus::Rejected => rejected_rewards += 100_000,
            }
        }
        
        Ok(StatusReport {
            confirmed_rewards,
            pending_rewards,
            rejected_rewards,
        })
    }
}

#[derive(Debug)]
pub enum SubmissionStatus {
    Pending,
    UnderReview,
    Confirmed,
    Rejected,
}

#[derive(Debug)]
pub struct StatusReport {
    pub confirmed_rewards: u64,
    pub pending_rewards: u64,
    pub rejected_rewards: u64,
}
