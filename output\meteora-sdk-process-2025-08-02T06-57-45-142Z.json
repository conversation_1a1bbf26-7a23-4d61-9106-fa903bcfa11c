{"metadata": {"timestamp": "2025-08-02T06:57:45.141Z", "totalCalls": 9, "pdaCalls": 8, "instructionCalls": 0, "transactionCalls": 1, "config": {"RPC_URL": "https://api.mainnet-beta.solana.com", "METEORA_PROGRAM_ID": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "LB_PAIR": "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "USER": "bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV", "TOKEN_X": "So11111111111111111111111111111111111111112", "TOKEN_Y": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "AMOUNT_X": 1000000, "AMOUNT_Y": 1000000, "BIN_LIQUIDITY_DIST": [{"binId": -4518, "xAmountBpsOfTotal": 3333, "yAmountBpsOfTotal": 3333}, {"binId": -4517, "xAmountBpsOfTotal": 3334, "yAmountBpsOfTotal": 3334}, {"binId": -4516, "xAmountBpsOfTotal": 3333, "yAmountBpsOfTotal": 3333}]}}, "processSequence": [{"callId": 1, "type": "PDA_GENERATION", "callType": "sync", "timestamp": *************, "executionTime": 3299900, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 4, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "706f736974696f6e", "base64": "cG9zaXRpb24=", "interpretation": "\"position\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "08dcb41ba23af1cca9b4c011839f33d258e8966f2fd11837d51d5f46e9d04f60", "base64": "CNy0G6I68cyptMARg58z0ljolm8v0Rg31R1fRunQT2A=", "interpretation": "PublicKey(bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV)"}, {"index": 3, "type": "<PERSON><PERSON><PERSON>", "length": 2, "hex": "0000", "base64": "AAA=", "interpretation": "i16(0)"}]}, "output": {"pda": "3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP", "bump": 254, "pdaBuffer": "2821feb348dc74b70904ae53e781095ca5a8363acb08c8c3efc2ff120b847b44"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:532:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 2, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865110, "executionTime": 2804300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 16, "hex": "6269746d61705f657874656e73696f6e", "base64": "Yml0bWFwX2V4dGVuc2lvbg==", "interpretation": "\"bitmap_extension\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 2, "hex": "0000", "base64": "AAA=", "interpretation": "i16(0)"}]}, "output": {"pda": "3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C", "bump": 253, "pdaBuffer": "24ff84fca00f1b952978b9ba2455a09d07834b3af8e48a66ef08eed34d0be51d"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:540:47)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 3, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865113, "executionTime": 949800, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 4, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865117, "executionTime": 2291300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 5, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865120, "executionTime": 1597300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 6, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865121, "executionTime": 399900, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 7, "hex": "72657365727665", "base64": "cmVzZXJ2ZQ==", "interpretation": "\"reserve\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "069b8857feab8184fb687f634618c035dac439dc1aeb3b5598a0f00000000001", "base64": "BpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAE=", "interpretation": "PublicKey(So11111111111111111111111111111111111111112)"}]}, "output": {"pda": "GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF", "bump": 255, "pdaBuffer": "ed61f5dc38dd7645716433d6c3e33a292e6aba9b14ee79f90679478c04ece01c"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:561:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 7, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865124, "executionTime": 578200, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 7, "hex": "72657365727665", "base64": "cmVzZXJ2ZQ==", "interpretation": "\"reserve\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "c6fa7af3<PERSON>bad3a3d65f36aabc97431b1bbe4c2d2f6e0e47ca60203452f5d61", "base64": "xvp6877brTo9ZfNqq8l0MbG75MLS9uDkfKYCA0UvXWE=", "interpretation": "PublicKey(EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v)"}]}, "output": {"pda": "Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD", "bump": 255, "pdaBuffer": "cd6888a592a0b7989d324a4662d689f9d8bf2514ca1be7419934414dfb40dc32"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:568:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 8, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865127, "executionTime": 655500, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 1, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 17, "hex": "5f5f6576656e745f617574686f72697479", "base64": "X19ldmVudF9hdXRob3JpdHk=", "interpretation": "\"__event_authority\""}]}, "output": {"pda": "D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6", "bump": 255, "pdaBuffer": "b270d67fa98c51cf0213051358962baf35742bed59c9d9445e9c0d0c85c7cd91"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:572:63)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 9, "type": "TRANSACTION_OPERATION", "operation": "add", "timestamp": *************, "executionTime": 105900, "input": {"instructionsCount": 1, "instructions": [{"index": 0, "programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "accountsCount": 17, "dataLength": 64}]}, "context": {"stackTrace": ["    at Transaction.add (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:146:14)", "    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:613:17)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)"]}}], "categorized": {"pdaGeneration": [{"callId": 1, "type": "PDA_GENERATION", "callType": "sync", "timestamp": *************, "executionTime": 3299900, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 4, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "706f736974696f6e", "base64": "cG9zaXRpb24=", "interpretation": "\"position\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "08dcb41ba23af1cca9b4c011839f33d258e8966f2fd11837d51d5f46e9d04f60", "base64": "CNy0G6I68cyptMARg58z0ljolm8v0Rg31R1fRunQT2A=", "interpretation": "PublicKey(bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV)"}, {"index": 3, "type": "<PERSON><PERSON><PERSON>", "length": 2, "hex": "0000", "base64": "AAA=", "interpretation": "i16(0)"}]}, "output": {"pda": "3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP", "bump": 254, "pdaBuffer": "2821feb348dc74b70904ae53e781095ca5a8363acb08c8c3efc2ff120b847b44"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:532:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 2, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865110, "executionTime": 2804300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 16, "hex": "6269746d61705f657874656e73696f6e", "base64": "Yml0bWFwX2V4dGVuc2lvbg==", "interpretation": "\"bitmap_extension\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 2, "hex": "0000", "base64": "AAA=", "interpretation": "i16(0)"}]}, "output": {"pda": "3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C", "bump": 253, "pdaBuffer": "24ff84fca00f1b952978b9ba2455a09d07834b3af8e48a66ef08eed34d0be51d"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:540:47)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 3, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865113, "executionTime": 949800, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 4, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865117, "executionTime": 2291300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 5, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865120, "executionTime": 1597300, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 9, "hex": "62696e5f6172726179", "base64": "YmluX2FycmF5", "interpretation": "\"bin_array\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 8, "hex": "bfffffffffffffff", "base64": "v/////////8=", "interpretation": "i64(-65)"}]}, "output": {"pda": "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "bump": 254, "pdaBuffer": "53bf7428431b65cec6b49b8975c96f76f3b73806aa62123d8b3464626e7be114"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:551:53)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 6, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865121, "executionTime": 399900, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 7, "hex": "72657365727665", "base64": "cmVzZXJ2ZQ==", "interpretation": "\"reserve\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "069b8857feab8184fb687f634618c035dac439dc1aeb3b5598a0f00000000001", "base64": "BpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAE=", "interpretation": "PublicKey(So11111111111111111111111111111111111111112)"}]}, "output": {"pda": "GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF", "bump": 255, "pdaBuffer": "ed61f5dc38dd7645716433d6c3e33a292e6aba9b14ee79f90679478c04ece01c"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:561:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 7, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865124, "executionTime": 578200, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 3, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 7, "hex": "72657365727665", "base64": "cmVzZXJ2ZQ==", "interpretation": "\"reserve\""}, {"index": 1, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043", "base64": "AT30dlK23U6yOL6KsjLw7pQFCMsuNUBmnP8AT6ZxEEM=", "interpretation": "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)"}, {"index": 2, "type": "<PERSON><PERSON><PERSON>", "length": 32, "hex": "c6fa7af3<PERSON>bad3a3d65f36aabc97431b1bbe4c2d2f6e0e47ca60203452f5d61", "base64": "xvp6877brTo9ZfNqq8l0MbG75MLS9uDkfKYCA0UvXWE=", "interpretation": "PublicKey(EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v)"}]}, "output": {"pda": "Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD", "bump": 255, "pdaBuffer": "cd6888a592a0b7989d324a4662d689f9d8bf2514ca1be7419934414dfb40dc32"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:568:51)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}, {"callId": 8, "type": "PDA_GENERATION", "callType": "sync", "timestamp": 1754117865127, "executionTime": 655500, "input": {"programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "seedsCount": 1, "seeds": [{"index": 0, "type": "<PERSON><PERSON><PERSON>", "length": 17, "hex": "5f5f6576656e745f617574686f72697479", "base64": "X19ldmVudF9hdXRob3JpdHk=", "interpretation": "\"__event_authority\""}]}, "output": {"pda": "D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6", "bump": 255, "pdaBuffer": "b270d67fa98c51cf0213051358962baf35742bed59c9d9445e9c0d0c85c7cd91"}, "context": {"stackTrace": ["    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:572:63)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)", "    at Object.<anonymous> (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:746:3)"], "isMeteoraPDA": true}}], "instructionCreation": [], "transactionOperations": [{"callId": 9, "type": "TRANSACTION_OPERATION", "operation": "add", "timestamp": *************, "executionTime": 105900, "input": {"instructionsCount": 1, "instructions": [{"index": 0, "programId": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "accountsCount": 17, "dataLength": 64}]}, "context": {"stackTrace": ["    at Transaction.add (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:146:14)", "    at MeteoraTransactionSimulator.createAddLiquidity2Transaction (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:613:17)", "    at main (H:\\Mempool\\DEXSWAP1\\meteora-sdk-process-interceptor.js:714:63)"]}}]}, "reproductionGuide": {"description": "Полное руководство по воспроизведению процесса создания add_liquidity2", "steps": [{"step": 1, "type": "PDA Generation", "description": "Generate PDA: 3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP", "seeds": ["\"position\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "PublicKey(bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV)", "i16(0)"], "code": "const seeds = [\n  Buffer.from('706f736974696f6e', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('08dcb41ba23af1cca9b4c011839f33d258e8966f2fd11837d51d5f46e9d04f60', 'hex'),\n  Buffer.from('0000', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: 3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP (bump: 254)"}, {"step": 2, "type": "PDA Generation", "description": "Generate PDA: 3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C", "seeds": ["\"bitmap_extension\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "i16(0)"], "code": "const seeds = [\n  Buffer.from('6269746d61705f657874656e73696f6e', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('0000', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: 3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C (bump: 253)"}, {"step": 3, "type": "PDA Generation", "description": "Generate PDA: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "seeds": ["\"bin_array\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "i64(-65)"], "code": "const seeds = [\n  Buffer.from('62696e5f6172726179', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('bfffffffffffffff', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF (bump: 254)"}, {"step": 4, "type": "PDA Generation", "description": "Generate PDA: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "seeds": ["\"bin_array\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "i64(-65)"], "code": "const seeds = [\n  Buffer.from('62696e5f6172726179', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('bfffffffffffffff', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF (bump: 254)"}, {"step": 5, "type": "PDA Generation", "description": "Generate PDA: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF", "seeds": ["\"bin_array\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "i64(-65)"], "code": "const seeds = [\n  Buffer.from('62696e5f6172726179', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('bfffffffffffffff', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: 6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF (bump: 254)"}, {"step": 6, "type": "PDA Generation", "description": "Generate PDA: GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF", "seeds": ["\"reserve\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "PublicKey(So11111111111111111111111111111111111111112)"], "code": "const seeds = [\n  Buffer.from('72657365727665', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('069b8857feab8184fb687f634618c035dac439dc1aeb3b5598a0f00000000001', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF (bump: 255)"}, {"step": 7, "type": "PDA Generation", "description": "Generate PDA: Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD", "seeds": ["\"reserve\"", "PublicKey(5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6)", "PublicKey(EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v)"], "code": "const seeds = [\n  Buffer.from('72657365727665', 'hex'),\n  Buffer.from('013df47652b6dd4eb238be8ab232f0ee940508cb2e3540669cff004fa6711043', 'hex'),\n  Buffer.from('c6fa7af3bedbad3a3d65f36aabc97431b1bbe4c2d2f6e0e47ca60203452f5d61', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD (bump: 255)"}, {"step": 8, "type": "PDA Generation", "description": "Generate PDA: D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6", "seeds": ["\"__event_authority\""], "code": "const seeds = [\n  Buffer.from('5f5f6576656e745f617574686f72697479', 'hex')\n];\nconst [pda, bump] = PublicKey.findProgramAddressSync(\n  seeds, \n  new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')\n);\n// Result: D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6 (bump: 255)"}], "codeExamples": []}}