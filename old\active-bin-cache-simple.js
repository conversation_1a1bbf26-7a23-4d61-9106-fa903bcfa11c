/**
 * 🔥 УПРОЩЕННЫЙ КЭШ ТОЛЬКО ДЛЯ АКТИВНОГО БИНА
 * Обновляется каждые 1000ms автоматически
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const dotenv = require('dotenv');

// 🔥 ЗАГРУЖАЕМ ВСЕ КЛЮЧИ ИЗ .env.solana
dotenv.config({ path: '.env.solana' });

class ActiveBinCacheSimple {
    constructor(connection = null) {
        // 🔥 ИСПОЛЬЗУЕМ HELIUS ИЗ .env.solana ЕСЛИ НЕ ПЕРЕДАН CONNECTION
        this.connection = connection || new Connection(
            process.env.HELIUS_RPC_URL || 'https://api.mainnet-beta.solana.com'
        );
        this.cache = new Map(); // poolAddress -> activeBinData
        this.updateInterval = null;
        this.isUpdating = false;
        
        // 🔥 КОНСТАНТЫ
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // 🔥 ПУЛЫ ДЛЯ МОНИТОРИНГА
        this.pools = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // POOL_1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'  // POOL_2
        ];
        
        console.log('🔥 УПРОЩЕННЫЙ КЭШ АКТИВНОГО БИНА ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔥 ЗАПУСК АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ КАЖДЫЕ 1000MS
     */
    startAutoUpdate() {
        console.log('🔥 ЗАПУСК АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ КАЖДЫЕ 1000MS');
        
        // Первое обновление сразу
        this.updateAllPools();
        
        // Затем каждые 1000ms
        this.updateInterval = setInterval(() => {
            if (!this.isUpdating) {
                this.updateAllPools();
            }
        }, 1000);
    }

    /**
     * 🔥 ОСТАНОВКА АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('🔥 АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ ОСТАНОВЛЕНО');
        }
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ ВСЕХ ПУЛОВ
     */
    async updateAllPools() {
        if (this.isUpdating) return;
        
        this.isUpdating = true;
        console.log('🔄 ОБНОВЛЕНИЕ АКТИВНЫХ БИНОВ...');
        
        try {
            const promises = this.pools.map(poolAddress => this.updatePool(poolAddress));
            await Promise.all(promises);
            console.log('✅ ВСЕ АКТИВНЫЕ БИНЫ ОБНОВЛЕНЫ');
        } catch (error) {
            console.error('❌ ОШИБКА ОБНОВЛЕНИЯ ПУЛОВ:', error.message);
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ ОДНОГО ПУЛА
     */
    async updatePool(poolAddress) {
        try {
            const poolPubkey = new PublicKey(poolAddress);
            
            // 🔥 СОЗДАЕМ DLMM INSTANCE
            const dlmmPool = await DLMM.create(this.connection, poolPubkey);
            
            // 🔥 ПОЛУЧАЕМ ТОЛЬКО АКТИВНЫЙ БИН
            const { activeBin, bins } = await dlmmPool.getBinsAroundActiveBin(0, 0);
            
            if (!bins || bins.length === 0) {
                throw new Error('Не удалось получить активный бин');
            }
            
            const activeBinData = bins[0];
            const activeBinId = dlmmPool.lbPair.activeId;
            
            // 🔥 ГЕНЕРИРУЕМ BIN ARRAY PDA
            const chunkIndex = Math.floor(activeBinId / 64);
            const indexBuffer = Buffer.alloc(8);
            indexBuffer.writeBigInt64LE(BigInt(chunkIndex), 0);
            
            const [binArrayPDA] = PublicKey.findProgramAddressSync([
                Buffer.from("bin_array"),
                poolPubkey.toBuffer(),
                indexBuffer
            ], this.METEORA_DLMM_PROGRAM);
            
            // 🔥 ГЕНЕРИРУЕМ BIN LIQUIDITY PDA
            const binIdBuffer = Buffer.alloc(4);
            binIdBuffer.writeInt32LE(activeBinId, 0);
            
            const [binLiquidityPDA] = PublicKey.findProgramAddressSync([
                Buffer.from("bin"),
                poolPubkey.toBuffer(),
                binIdBuffer
            ], this.METEORA_DLMM_PROGRAM);
            
            // 🔥 СОХРАНЯЕМ В КЭШ
            const cacheData = {
                poolAddress,
                activeBinId,
                activeBin: {
                    binId: activeBinId,
                    price: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                    pricePerToken: parseFloat(activeBinData.pricePerToken || activeBinData.price || 0),
                    isActive: true,
                    xAmount: activeBinData.xAmount || 0,
                    yAmount: activeBinData.yAmount || 0,
                    supply: activeBinData.supply || 0,
                    liquidityX: activeBinData.xAmount || 0,
                    liquidityY: activeBinData.yAmount || 0
                },
                binArrayPDA: binArrayPDA.toString(),
                binLiquidityPDA: binLiquidityPDA.toString(),
                chunkIndex,
                timestamp: Date.now(),
                dlmmPool // Сохраняем DLMM instance для swap
            };
            
            this.cache.set(poolAddress, cacheData);
            
            console.log(`✅ ${poolAddress.slice(0,8)}: активный бин ${activeBinId}, цена ${cacheData.activeBin.price}`);
            
        } catch (error) {
            console.error(`❌ ОШИБКА ОБНОВЛЕНИЯ ${poolAddress.slice(0,8)}:`, error.message);
        }
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ДАННЫХ АКТИВНОГО БИНА
     */
    getActiveBin(poolAddress) {
        const data = this.cache.get(poolAddress);
        if (!data) {
            console.log(`❌ НЕТ ДАННЫХ ДЛЯ ${poolAddress.slice(0,8)}`);
            return null;
        }
        
        // Проверяем свежесть данных (не старше 5 секунд)
        const age = Date.now() - data.timestamp;
        if (age > 5000) {
            console.log(`⚠️ ДАННЫЕ УСТАРЕЛИ ДЛЯ ${poolAddress.slice(0,8)} (${age}ms)`);
        }
        
        return data;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ DLMM INSTANCE ДЛЯ SWAP
     */
    getDLMMPool(poolAddress) {
        const data = this.cache.get(poolAddress);
        return data ? data.dlmmPool : null;
    }

    /**
     * 🔥 ПРОВЕРКА ГОТОВНОСТИ КЭША
     */
    isReady() {
        return this.pools.every(poolAddress => {
            const data = this.cache.get(poolAddress);
            return data && (Date.now() - data.timestamp) < 5000;
        });
    }

    /**
     * 🔥 СТАТИСТИКА КЭША
     */
    getStats() {
        const stats = {
            totalPools: this.pools.length,
            cachedPools: this.cache.size,
            ready: this.isReady(),
            pools: {}
        };
        
        this.pools.forEach(poolAddress => {
            const data = this.cache.get(poolAddress);
            stats.pools[poolAddress.slice(0,8)] = {
                cached: !!data,
                activeBinId: data ? data.activeBinId : null,
                price: data ? data.activeBin.price : null,
                age: data ? Date.now() - data.timestamp : null
            };
        });
        
        return stats;
    }
}

module.exports = ActiveBinCacheSimple;
