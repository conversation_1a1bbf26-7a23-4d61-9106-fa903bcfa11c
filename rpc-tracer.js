/**
 * 🔍 ДЕТАЛЬНЫЙ ТРЕЙСЕР ВСЕХ RPC ЗАПРОСОВ
 * Перехватывает ВСЕ RPC вызовы и показывает откуда они идут
 */

let rpcCallCount = 0;
const rpcCalls = [];

// Функция для получения стека вызовов
function getCallStack() {
    const stack = new Error().stack;
    return stack.split('\n').slice(2, 6).map(line => line.trim()).join(' → ');
}

// Перехватчик для Connection методов
function interceptConnection(connection, name) {
    console.log(`🔍 ПЕРЕХВАТЫВАЕМ CONNECTION: ${name}`);
    
    const originalMethods = {};
    
    // Список всех RPC методов которые нужно перехватить
    const rpcMethods = [
        'getLatestBlockhash',
        'sendRawTransaction', 
        'sendTransaction',
        'getAccountInfo',
        'getMultipleAccountsInfo',
        'getBalance',
        'getTokenAccountBalance',
        'getTokenAccountsByOwner',
        'getProgramAccounts',
        'getSlot',
        'getVersion',
        'simulateTransaction',
        'confirmTransaction'
    ];
    
    rpcMethods.forEach(method => {
        if (typeof connection[method] === 'function') {
            originalMethods[method] = connection[method].bind(connection);
            
            connection[method] = async function(...args) {
                rpcCallCount++;
                const callInfo = {
                    count: rpcCallCount,
                    method,
                    connection: name,
                    timestamp: Date.now(),
                    stack: getCallStack(),
                    args: args.length
                };
                rpcCalls.push(callInfo);
                
                console.log(`📞 RPC ВЫЗОВ #${rpcCallCount}: ${method} (${name})`);
                console.log(`   📍 Откуда: ${callInfo.stack}`);
                console.log(`   ⏰ Время: ${new Date(callInfo.timestamp).toISOString()}`);
                
                try {
                    const result = await originalMethods[method](...args);
                    console.log(`   ✅ ${method} успешен`);
                    return result;
                } catch (error) {
                    console.log(`   ❌ ${method} ошибка: ${error.message}`);
                    throw error;
                }
            };
        }
    });
    
    return connection;
}

// Перехватчик для globalRPCManager
function interceptRPCManager() {
    console.log('🔍 ПЕРЕХВАТЫВАЕМ GLOBAL RPC MANAGER...');
    
    const { globalRPCManager } = require('./centralized-rpc-manager.js');
    
    // Перехватываем getConnectionByType
    const originalGetConnectionByType = globalRPCManager.getConnectionByType;
    globalRPCManager.getConnectionByType = async function(...args) {
        rpcCallCount++;
        const callInfo = {
            count: rpcCallCount,
            method: 'getConnectionByType',
            connection: 'globalRPCManager',
            timestamp: Date.now(),
            stack: getCallStack(),
            args: args
        };
        rpcCalls.push(callInfo);
        
        console.log(`📞 RPC ВЫЗОВ #${rpcCallCount}: getConnectionByType (${args[0]})`);
        console.log(`   📍 Откуда: ${callInfo.stack}`);
        
        const connection = await originalGetConnectionByType.apply(this, args);
        
        // Перехватываем созданное подключение
        return interceptConnection(connection, `RPC-${args[0]}-${rpcCallCount}`);
    };
    
    // Перехватываем getLatestBlockhash
    const originalGetLatestBlockhash = globalRPCManager.getLatestBlockhash;
    globalRPCManager.getLatestBlockhash = async function(...args) {
        rpcCallCount++;
        const callInfo = {
            count: rpcCallCount,
            method: 'getLatestBlockhash',
            connection: 'globalRPCManager',
            timestamp: Date.now(),
            stack: getCallStack(),
            args: args
        };
        rpcCalls.push(callInfo);
        
        console.log(`📞 RPC ВЫЗОВ #${rpcCallCount}: getLatestBlockhash (globalRPCManager)`);
        console.log(`   📍 Откуда: ${callInfo.stack}`);
        
        try {
            const result = await originalGetLatestBlockhash.apply(this, args);
            console.log(`   ✅ getLatestBlockhash успешен`);
            return result;
        } catch (error) {
            console.log(`   ❌ getLatestBlockhash ошибка: ${error.message}`);
            throw error;
        }
    };
    
    // Перехватываем sendTransaction
    const originalSendTransaction = globalRPCManager.sendTransaction;
    globalRPCManager.sendTransaction = async function(...args) {
        rpcCallCount++;
        const callInfo = {
            count: rpcCallCount,
            method: 'sendTransaction',
            connection: 'globalRPCManager',
            timestamp: Date.now(),
            stack: getCallStack(),
            args: args.length
        };
        rpcCalls.push(callInfo);
        
        console.log(`📞 RPC ВЫЗОВ #${rpcCallCount}: sendTransaction (globalRPCManager)`);
        console.log(`   📍 Откуда: ${callInfo.stack}`);
        
        try {
            const result = await originalSendTransaction.apply(this, args);
            console.log(`   ✅ sendTransaction успешен`);
            return result;
        } catch (error) {
            console.log(`   ❌ sendTransaction ошибка: ${error.message}`);
            throw error;
        }
    };
}

// Функция для показа итогового отчета
function showRPCReport() {
    console.log('\n🔍 ИТОГОВЫЙ ОТЧЕТ RPC ВЫЗОВОВ:');
    console.log(`   Всего вызовов: ${rpcCallCount}`);
    
    if (rpcCallCount <= 2) {
        console.log('✅ ОТЛИЧНО! Не больше 2 RPC вызовов');
    } else {
        console.log('❌ ПРОБЛЕМА! Слишком много RPC вызовов');
    }
    
    console.log('\n📋 ДЕТАЛИ ВСЕХ ВЫЗОВОВ:');
    rpcCalls.forEach((call, index) => {
        console.log(`   ${index + 1}. ${call.method} (${call.connection})`);
        console.log(`      📍 ${call.stack}`);
        console.log(`      ⏰ ${new Date(call.timestamp).toISOString()}`);
    });
    
    return rpcCallCount;
}

// Экспорт
module.exports = {
    interceptRPCManager,
    interceptConnection,
    showRPCReport,
    getRPCCallCount: () => rpcCallCount,
    getRPCCalls: () => rpcCalls
};

// Автоматический запуск перехвата при импорте
interceptRPCManager();
