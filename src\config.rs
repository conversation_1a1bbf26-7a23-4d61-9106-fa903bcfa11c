use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fs;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub rpc_url: String,
    pub websocket_url: String,
    pub max_threads: usize,
    pub scan_interval_seconds: u64,
    pub bug_bounty_programs: Vec<BugBountyProgram>,
    pub email_config: EmailConfig,
    pub database_path: String,
    pub output_directory: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BugBountyProgram {
    pub name: String,
    pub email: Option<String>,
    pub api_endpoint: Option<String>,
    pub api_key: Option<String>,
    pub max_reward: u64,
    pub submission_format: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    pub smtp_server: String,
    pub smtp_port: u16,
    pub username: String,
    pub password: String,
    pub from_address: String,
}

impl Config {
    pub async fn load() -> Result<Self> {
        // Пытаемся загрузить конфигурацию из файла
        if let Ok(config_str) = fs::read_to_string("config.toml") {
            if let Ok(config) = toml::from_str(&config_str) {
                return Ok(config);
            }
        }
        
        // Если файл не найден, создаем конфигурацию по умолчанию
        let default_config = Self::default();
        
        // Сохраняем конфигурацию по умолчанию в файл
        let config_str = toml::to_string_pretty(&default_config)?;
        fs::write("config.toml", config_str)?;
        
        println!("📝 Created default config.toml - please review and update");
        
        Ok(default_config)
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            rpc_url: "https://api.devnet.solana.com".to_string(),
            websocket_url: "wss://api.devnet.solana.com".to_string(),
            max_threads: 8,
            scan_interval_seconds: 60,
            bug_bounty_programs: vec![
                BugBountyProgram {
                    name: "Solana Foundation".to_string(),
                    email: Some("<EMAIL>".to_string()),
                    api_endpoint: None,
                    api_key: None,
                    max_reward: 1_000_000,
                    submission_format: "email".to_string(),
                },
                BugBountyProgram {
                    name: "Immunefi".to_string(),
                    email: None,
                    api_endpoint: Some("https://immunefi.com/api/submit".to_string()),
                    api_key: Some("YOUR_IMMUNEFI_API_KEY".to_string()),
                    max_reward: 1_000_000,
                    submission_format: "api".to_string(),
                },
                BugBountyProgram {
                    name: "Jupiter".to_string(),
                    email: Some("<EMAIL>".to_string()),
                    api_endpoint: None,
                    api_key: None,
                    max_reward: 500_000,
                    submission_format: "email".to_string(),
                },
                BugBountyProgram {
                    name: "Marinade Finance".to_string(),
                    email: Some("<EMAIL>".to_string()),
                    api_endpoint: None,
                    api_key: None,
                    max_reward: 1_000_000,
                    submission_format: "email".to_string(),
                },
            ],
            email_config: EmailConfig {
                smtp_server: "smtp.gmail.com".to_string(),
                smtp_port: 587,
                username: "<EMAIL>".to_string(),
                password: "your-app-password".to_string(),
                from_address: "<EMAIL>".to_string(),
            },
            database_path: "bug_hunter.db".to_string(),
            output_directory: "output".to_string(),
        }
    }
}
