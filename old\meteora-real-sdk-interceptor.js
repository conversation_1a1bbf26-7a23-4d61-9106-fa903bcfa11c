/**
 * 🎯 METEORA REAL SDK INTERCEPTOR
 * ПЕРЕХВАТ РЕАЛЬНОГО SDK METEORA ДЛЯ ОТСЛЕЖИВАНИЯ СОЗДАНИЯ PDA
 * Использует настоящий @meteora-ag/dlmm SDK
 */

const { PublicKey, Connection, Keypair } = require('@solana/web3.js');
const BN = require('bn.js');
const fs = require('fs');

// === CONFIGURATION ===
const CONFIG = {
  RPC_URL: 'https://api.mainnet-beta.solana.com',
  METEORA_PROGRAM_ID: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  
  // Ваши данные
  LB_PAIR: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // WSOL-USDC
  USER: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
  
  // Ожидаемые PDA из вашей инструкции
  EXPECTED_PDAS: {
    position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
    bitmapExtension: '4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH',
    remainingAccount: 'Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw'
  }
};

// === ГЛОБАЛЬНЫЙ ПЕРЕХВАТЧИК ===
class GlobalPDAInterceptor {
  constructor() {
    this.interceptedCalls = [];
    this.isActive = false;
    this.originalMethods = {};
  }

  /**
   * 🔧 УСТАНОВКА ГЛОБАЛЬНОГО ПЕРЕХВАТЧИКА
   */
  install() {
    console.log('🔧 УСТАНОВКА ГЛОБАЛЬНОГО ПЕРЕХВАТЧИКА...');
    
    // Сохраняем оригинальные методы
    this.originalMethods.findProgramAddress = PublicKey.findProgramAddress;
    this.originalMethods.findProgramAddressSync = PublicKey.findProgramAddressSync;
    
    const self = this;
    
    // Перехватываем асинхронную версию
    PublicKey.findProgramAddress = async function(seeds, programId) {
      const result = await self.originalMethods.findProgramAddress.call(this, seeds, programId);
      self.interceptCall(seeds, programId, result, 'async');
      return result;
    };
    
    // Перехватываем синхронную версию
    PublicKey.findProgramAddressSync = function(seeds, programId) {
      const result = self.originalMethods.findProgramAddressSync.call(this, seeds, programId);
      self.interceptCall(seeds, programId, result, 'sync');
      return result;
    };
    
    this.isActive = true;
    console.log('✅ Глобальный перехватчик установлен');
  }

  /**
   * 📝 ПЕРЕХВАТ ВЫЗОВА
   */
  interceptCall(seeds, programId, result, callType) {
    if (!this.isActive) return;
    
    const [pda, bump] = result;
    const programIdStr = programId.toBase58();
    const pdaStr = pda.toBase58();
    
    // Фильтруем только Meteora вызовы или совпадающие PDA
    const isMeteoraPDA = programIdStr === CONFIG.METEORA_PROGRAM_ID;
    const matchedExpected = Object.entries(CONFIG.EXPECTED_PDAS).find(([key, expectedPda]) => 
      expectedPda === pdaStr
    );
    
    if (!isMeteoraPDA && !matchedExpected) return;
    
    // Анализируем seeds
    const seedsAnalysis = this.analyzeSeeds(seeds);
    
    const callInfo = {
      timestamp: Date.now(),
      callType,
      programId: programIdStr,
      pda: pdaStr,
      bump,
      isMeteoraPDA,
      matchedExpected: matchedExpected ? matchedExpected[0] : null,
      seedsCount: seeds.length,
      seeds: seedsAnalysis,
      stackTrace: this.getCleanStackTrace()
    };
    
    this.interceptedCalls.push(callInfo);
    
    // Выводим важные находки
    console.log(`\n🎯 ПЕРЕХВАЧЕН ${isMeteoraPDA ? 'METEORA' : 'ЦЕЛЕВОЙ'} PDA:`);
    console.log(`   PDA: ${pdaStr}`);
    console.log(`   Bump: ${bump}`);
    
    if (matchedExpected) {
      console.log(`   🔥 СОВПАДЕНИЕ: ${matchedExpected[0].toUpperCase()}`);
    }
    
    console.log(`   Seeds (${seeds.length}):`);
    seedsAnalysis.forEach(seed => {
      console.log(`     [${seed.index}] ${seed.decoded} (${seed.length}b)`);
      console.log(`         hex: ${seed.hex}`);
    });
  }

  /**
   * 🔍 АНАЛИЗ SEEDS
   */
  analyzeSeeds(seeds) {
    return seeds.map((seed, index) => {
      const isBuffer = Buffer.isBuffer(seed);
      const hex = isBuffer ? seed.toString('hex') : '';
      const length = isBuffer ? seed.length : 0;
      
      let decoded = '';
      if (isBuffer) {
        decoded = this.decodeSeed(seed);
      } else {
        decoded = `${typeof seed}(${seed})`;
      }
      
      return {
        index,
        type: isBuffer ? 'Buffer' : typeof seed,
        length,
        hex,
        decoded,
        raw: isBuffer ? Array.from(seed) : seed
      };
    });
  }

  /**
   * 🔍 ДЕКОДИРОВАНИЕ SEED
   */
  decodeSeed(seed) {
    try {
      // Пробуем как UTF-8 строку
      const str = seed.toString('utf8');
      if (/^[\x20-\x7E]+$/.test(str)) {
        return `"${str}"`;
      }
    } catch {}
    
    // Анализируем как числа
    if (seed.length === 1) {
      return `u8(${seed[0]})`;
    } else if (seed.length === 2) {
      const num = seed.readInt16LE(0);
      const unum = seed.readUInt16LE(0);
      return `i16(${num})/u16(${unum})`;
    } else if (seed.length === 4) {
      const num = seed.readInt32LE(0);
      const unum = seed.readUInt32LE(0);
      return `i32(${num})/u32(${unum})`;
    } else if (seed.length === 8) {
      try {
        const num = seed.readBigInt64LE(0);
        const unum = seed.readBigUInt64LE(0);
        return `i64(${num})/u64(${unum})`;
      } catch {
        return `8bytes(${seed.toString('hex')})`;
      }
    } else if (seed.length === 32) {
      try {
        const pubkey = new PublicKey(seed);
        return `PublicKey(${pubkey.toBase58()})`;
      } catch {
        return `32bytes(${seed.toString('hex').slice(0, 16)}...)`;
      }
    }
    
    return `${seed.length}bytes(${seed.toString('hex')})`;
  }

  /**
   * 📍 ПОЛУЧЕНИЕ ЧИСТОГО СТЕКА
   */
  getCleanStackTrace() {
    const stack = new Error().stack;
    if (!stack) return [];
    
    return stack.split('\n')
      .slice(1)
      .filter(line => 
        !line.includes('GlobalPDAInterceptor') && 
        !line.includes('findProgramAddress') &&
        line.trim().length > 0
      )
      .slice(0, 5); // Первые 5 релевантных строк
  }

  /**
   * 🛑 УДАЛЕНИЕ ПЕРЕХВАТЧИКА
   */
  uninstall() {
    if (this.isActive) {
      PublicKey.findProgramAddress = this.originalMethods.findProgramAddress;
      PublicKey.findProgramAddressSync = this.originalMethods.findProgramAddressSync;
      this.isActive = false;
      console.log('🛑 Глобальный перехватчик удален');
    }
  }

  /**
   * 📊 АНАЛИЗ РЕЗУЛЬТАТОВ
   */
  getResults() {
    const meteoraCalls = this.interceptedCalls.filter(call => call.isMeteoraPDA);
    const matchedCalls = this.interceptedCalls.filter(call => call.matchedExpected);
    
    return {
      total: this.interceptedCalls.length,
      meteora: meteoraCalls.length,
      matched: matchedCalls.length,
      calls: this.interceptedCalls,
      matchedCalls
    };
  }

  /**
   * 💾 ЭКСПОРТ РЕЗУЛЬТАТОВ
   */
  exportResults() {
    const results = {
      timestamp: new Date().toISOString(),
      config: CONFIG,
      results: this.getResults()
    };
    
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const filename = `meteora-real-sdk-intercept-${Date.now()}.json`;
    const filePath = `${outputDir}/${filename}`;
    fs.writeFileSync(filePath, JSON.stringify(results, null, 2));
    
    console.log(`\n💾 Результаты сохранены: ${filePath}`);
    return filePath;
  }
}

// === ТЕСТИРОВАНИЕ С РЕАЛЬНЫМИ ПАРАМЕТРАМИ ===
class RealParameterTester {
  
  /**
   * 🧪 ТЕСТИРУЕМ РЕАЛЬНЫЕ ПАРАМЕТРЫ
   */
  static async testRealParameters() {
    console.log('\n🧪 ТЕСТИРОВАНИЕ С РЕАЛЬНЫМИ ПАРАМЕТРАМИ...');
    
    const lbPair = new PublicKey(CONFIG.LB_PAIR);
    const user = new PublicKey(CONFIG.USER);
    const programId = new PublicKey(CONFIG.METEORA_PROGRAM_ID);
    
    // Тестируем различные комбинации seeds
    await this.testPositionVariants(lbPair, user, programId);
    await this.testBitmapVariants(lbPair, programId);
    await this.testBinArrayVariants(lbPair, programId);
    await this.testOtherVariants(lbPair, user, programId);
  }
  
  static async testPositionVariants(lbPair, user, programId) {
    console.log('🔍 Тестируем Position варианты...');
    
    const prefixes = ['position', 'Position', 'user_position', 'lb_position'];
    
    for (const prefix of prefixes) {
      for (let i = 0; i < 10; i++) {
        // Разные порядки и кодирования
        const variants = [
          [Buffer.from(prefix), lbPair.toBuffer(), user.toBuffer(), this.encodeU16(i)],
          [Buffer.from(prefix), user.toBuffer(), lbPair.toBuffer(), this.encodeU16(i)],
          [Buffer.from(prefix), lbPair.toBuffer(), user.toBuffer(), this.encodeU32(i)],
          [Buffer.from(prefix), lbPair.toBuffer(), user.toBuffer(), this.encodeU64(i)],
          [Buffer.from(prefix), lbPair.toBuffer(), user.toBuffer()], // без индекса
        ];
        
        for (const seeds of variants) {
          try {
            PublicKey.findProgramAddressSync(seeds, programId);
          } catch (e) {
            // Игнорируем ошибки
          }
        }
      }
    }
  }
  
  static async testBitmapVariants(lbPair, programId) {
    console.log('🔍 Тестируем Bitmap варианты...');
    
    const prefixes = ['bitmap_extension', 'BitmapExtension', 'bin_array_bitmap_extension'];
    const binIds = [-4517, -4516, -4518, -512, 0, 512]; // Вероятные значения
    
    for (const prefix of prefixes) {
      for (const binId of binIds) {
        // Разные выравнивания
        const alignments = [1, 64, 128, 256, 512];
        
        for (const alignment of alignments) {
          const alignedBinId = Math.floor(binId / alignment) * alignment;
          
          const variants = [
            [Buffer.from(prefix), lbPair.toBuffer(), this.encodeI16(alignedBinId)],
            [Buffer.from(prefix), lbPair.toBuffer(), this.encodeU16(alignedBinId)],
            [Buffer.from(prefix), lbPair.toBuffer(), this.encodeI32(alignedBinId)],
            [Buffer.from(prefix), lbPair.toBuffer(), this.encodeU32(alignedBinId)],
          ];
          
          for (const seeds of variants) {
            try {
              PublicKey.findProgramAddressSync(seeds, programId);
            } catch (e) {
              // Игнорируем ошибки
            }
          }
        }
      }
    }
  }
  
  static async testBinArrayVariants(lbPair, programId) {
    console.log('🔍 Тестируем Bin Array варианты...');
    
    const prefixes = ['bin_array', 'BinArray', 'bin', 'array'];
    
    for (const prefix of prefixes) {
      for (let index = -100; index <= 100; index++) {
        const variants = [
          [Buffer.from(prefix), lbPair.toBuffer(), this.encodeI64(index)],
          [Buffer.from(prefix), lbPair.toBuffer(), this.encodeU64(index)],
          [Buffer.from(prefix), lbPair.toBuffer(), this.encodeI32(index)],
          [Buffer.from(prefix), lbPair.toBuffer(), this.encodeU32(index)],
        ];
        
        for (const seeds of variants) {
          try {
            PublicKey.findProgramAddressSync(seeds, programId);
          } catch (e) {
            // Игнорируем ошибки
          }
        }
      }
    }
  }
  
  static async testOtherVariants(lbPair, user, programId) {
    console.log('🔍 Тестируем другие варианты...');
    
    // Event Authority
    const eventPrefixes = ['__event_authority', 'event_authority', 'EventAuthority'];
    for (const prefix of eventPrefixes) {
      try {
        PublicKey.findProgramAddressSync([Buffer.from(prefix)], programId);
      } catch (e) {}
    }
    
    // Reserve
    const tokens = [
      'So11111111111111111111111111111111111111112', // WSOL
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  // USDC
    ];
    
    for (const tokenStr of tokens) {
      const token = new PublicKey(tokenStr);
      try {
        PublicKey.findProgramAddressSync([
          Buffer.from('reserve'),
          lbPair.toBuffer(),
          token.toBuffer()
        ], programId);
      } catch (e) {}
    }
  }
  
  // Утилиты кодирования
  static encodeU16(value) {
    const buf = Buffer.alloc(2);
    buf.writeUInt16LE(value, 0);
    return buf;
  }
  
  static encodeI16(value) {
    const buf = Buffer.alloc(2);
    buf.writeInt16LE(value, 0);
    return buf;
  }
  
  static encodeU32(value) {
    const buf = Buffer.alloc(4);
    buf.writeUInt32LE(value, 0);
    return buf;
  }
  
  static encodeI32(value) {
    const buf = Buffer.alloc(4);
    buf.writeInt32LE(value, 0);
    return buf;
  }
  
  static encodeU64(value) {
    const buf = Buffer.alloc(8);
    buf.writeBigUInt64LE(BigInt(value), 0);
    return buf;
  }
  
  static encodeI64(value) {
    const buf = Buffer.alloc(8);
    buf.writeBigInt64LE(BigInt(value), 0);
    return buf;
  }
}

// === MAIN EXECUTION ===
async function main() {
  console.log('🎯 METEORA REAL SDK INTERCEPTOR');
  console.log('ПЕРЕХВАТ РЕАЛЬНОГО ПРОЦЕССА СОЗДАНИЯ PDA');
  console.log('='.repeat(60));
  
  const interceptor = new GlobalPDAInterceptor();
  
  try {
    // Устанавливаем перехватчик
    interceptor.install();
    
    // Тестируем с реальными параметрами
    await RealParameterTester.testRealParameters();
    
    // Анализируем результаты
    const results = interceptor.getResults();
    
    console.log('\n📊 РЕЗУЛЬТАТЫ ПЕРЕХВАТА:');
    console.log('='.repeat(40));
    console.log(`Всего вызовов: ${results.total}`);
    console.log(`Meteora PDA: ${results.meteora}`);
    console.log(`Совпадений: ${results.matched}`);
    
    if (results.matched > 0) {
      console.log('\n🎯 НАЙДЕННЫЕ СОВПАДЕНИЯ:');
      results.matchedCalls.forEach(call => {
        console.log(`\n${call.matchedExpected.toUpperCase()}:`);
        console.log(`  PDA: ${call.pda}`);
        console.log(`  Формула: ${call.seeds.map(s => s.decoded).join(' + ')}`);
      });
    }
    
    // Экспортируем результаты
    interceptor.exportResults();
    
    console.log('\n✅ ПЕРЕХВАТ ЗАВЕРШЕН!');
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  } finally {
    interceptor.uninstall();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  GlobalPDAInterceptor,
  RealParameterTester,
  CONFIG
};
