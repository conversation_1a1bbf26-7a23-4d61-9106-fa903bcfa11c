/**
 * 🔑 СОЗДАНИЕ ФАЙЛА С ПРИВАТНЫМ КЛЮЧОМ
 * Конвертирует base58 приватный ключ в JSON формат для тестирования
 */

const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// Ваш приватный ключ в base58 формате
const PRIVATE_KEY_BASE58 = 'ВСТАВЬТЕ_СЮДА_ВАШ_ПРИВАТНЫЙ_КЛЮЧ';

function createWalletKeyFile() {
    try {
        if (PRIVATE_KEY_BASE58 === 'ВСТАВЬТЕ_СЮДА_ВАШ_ПРИВАТНЫЙ_КЛЮЧ') {
            console.log('❌ НУЖНО ВСТАВИТЬ РЕАЛЬНЫЙ ПРИВАТНЫЙ КЛЮЧ');
            console.log('💡 Откройте create-wallet-key-file.js и вставьте ваш приватный ключ');
            return;
        }

        // Конвертируем base58 в Keypair
        const keypair = Keypair.fromSecretKey(
            require('bs58').decode(PRIVATE_KEY_BASE58)
        );

        // Сохраняем в JSON формате
        const secretKeyArray = Array.from(keypair.secretKey);
        
        fs.writeFileSync('wallet-private-key.json', JSON.stringify(secretKeyArray, null, 2));
        
        console.log('✅ ФАЙЛ СОЗДАН: wallet-private-key.json');
        console.log(`📍 Публичный ключ: ${keypair.publicKey.toBase58()}`);
        console.log('🔒 Приватный ключ сохранен в безопасном формате');
        
    } catch (error) {
        console.error('❌ ОШИБКА:', error.message);
        console.log('💡 Проверьте правильность приватного ключа');
    }
}

if (require.main === module) {
    createWalletKeyFile();
}

module.exports = { createWalletKeyFile };
