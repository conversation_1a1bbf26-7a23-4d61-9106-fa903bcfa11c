/**
 * � ЦЕНТРАЛИЗОВАННАЯ КОНФИГУРАЦИЯ RPC ПРОВАЙДЕРОВ
 * ВСЕ RPC КЛЮЧИ ТОЛЬКО ОТСЮДА! НИГДЕ БОЛЬШЕ!
 */

const fs = require('fs');
const path = require('path');

class RPCConfig {
    constructor() {
        this.config = null;
        this.loadConfig();
    }

    /**
     * 🔥 ЗАГРУЗКА КОНФИГУРАЦИИ ИЗ .env.solana
     */
    loadConfig() {
        try {
            const envPath = path.join(__dirname, '.env.solana');
            if (!fs.existsSync(envPath)) {
                throw new Error('❌ Файл .env.solana не найден!');
            }

            const envContent = fs.readFileSync(envPath, 'utf8');
            const envVars = {};
            
            // Парсим .env файл
            envContent.split('\n').forEach(line => {
                line = line.trim();
                if (line && !line.startsWith('#') && line.includes('=')) {
                    const [key, ...valueParts] = line.split('=');
                    const value = valueParts.join('=').trim();
                    envVars[key.trim()] = value;
                }
            });

            // 🔥 КОНФИГУРАЦИЯ RPC ПРОВАЙДЕРОВ
            this.config = {
                // 🚀 TRANSACTION RPC (ТОЛЬКО ДЛЯ ОТПРАВКИ ТРАНЗАКЦИЙ)
                transaction: {
                    url: envVars.TRANSACTION_RPC_URL || 'https://api.mainnet-beta.solana.com',
                    name: 'Solana Mainnet Transaction',
                    maxRequestsPerSecond: 10,
                    timeout: 15000,
                    priority: 1
                },

                // 📊 DATA RPC ПРОВАЙДЕРЫ (ДЛЯ ПОЛУЧЕНИЯ ДАННЫХ)
                data: [
                    // ❌ SYNDICA ОТКЛЮЧЕН - НЕ РАБОТАЕТ (401 Unauthorized)
                    // {
                    //     url: envVars.SYNDICA_RPC_URL,
                    //     name: 'Syndica High Performance',
                    //     maxRequestsPerSecond: 20,
                    //     timeout: 5000,
                    //     priority: 1,
                    //     enabled: false // ❌ ОТКЛЮЧЕН!
                    // },
                    // {
                    //     url: envVars.HELIUS_RPC_URL,
                    //     name: 'Helius Primary',
                    //     maxRequestsPerSecond: 10,
                    //     timeout: 8000,
                    //     priority: 1, // ОТКЛЮЧЕН!
                    //     enabled: false // ❌ ОТКЛЮЧЕН!
                    // },
                    {
                        url: 'https://bold-omniscient-arm.solana-mainnet.quiknode.pro/b11b7e86a3929c21297a0e50515cca99b1fd218f/',
                        name: 'QuickNode Data Only',
                        maxRequestsPerSecond: 15,
                        timeout: 6000,
                        priority: 2, // ДЛЯ ДАННЫХ, НЕ ДЛЯ ТРАНЗАКЦИЙ!
                        enabled: true,
                        note: 'ТОЛЬКО ДЛЯ ПОЛУЧЕНИЯ ДАННЫХ! НЕ ДЛЯ ТРАНЗАКЦИЙ!',
                        supportedMethods: [
                            'getVersion', 'getSlot', 'getLatestBlockhash',
                            'getAccountInfo', 'getMultipleAccountsInfo',
                            'getBalance', 'getTokenAccountsByOwner'
                        ],
                        unsupportedMethods: [
                            'sendTransaction', 'simulateTransaction',
                            'getProgramAccounts' // ❌ Исключен из индекса
                        ]
                    }
                ].filter(provider => provider.enabled && provider.url),

                // 🔑 WALLET КОНФИГУРАЦИЯ
                wallet: {
                    address: envVars.WALLET_ADDRESS,
                    privateKey: envVars.WALLET_PRIVATE_KEY,
                    seedPhrase: envVars.WALLET_SEED_PHRASE
                },

                // 🏦 MARGINFI КОНФИГУРАЦИЯ
                marginfi: {
                    programId: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdmPFc',
                    group: '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8'
                },

                // 🌪️ METEORA КОНФИГУРАЦИЯ
                meteora: {
                    programId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',

                    // 🔥 ФИКСИРОВАННЫЕ РАБОЧИЕ АДРЕСА! НЕ ТРОГАТЬ!
                    pools: {
                        // 🔥 СУЩЕСТВУЮЩИЕ РАБОЧИЕ АДРЕСА (ПРОВЕРЕНЫ В БЛОКЧЕЙНЕ!)
                        POOL_1: {
                            lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // ✅ СУЩЕСТВУЕТ
                            reserveX: 'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // ✅ СУЩЕСТВУЕТ
                            reserveY: 'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz' // ✅ СУЩЕСТВУЕТ
                        },
                        POOL_2: {
                            lbPair: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // ✅ СУЩЕСТВУЕТ
                            reserveX: 'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // ✅ СУЩЕСТВУЕТ
                            reserveY: '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb' // ✅ СУЩЕСТВУЕТ
                        }
                    },

                    // 🔥 ОБЩИЕ METEORA АДРЕСА
                    eventAuthority: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6', // ИЗ ОШИБКИ 2006!

                    // 🔥 TOKEN MINTS
                    tokens: {
                        WSOL: 'So11111111111111111111111111111111111111112',
                        USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
                    }
                }
            };

            console.log('✅ RPC конфигурация загружена из .env.solana');
            console.log(`📊 Доступно DATA провайдеров: ${this.config.data.length}`);
            console.log(`🚀 TRANSACTION провайдер: ${this.config.transaction.name}`);

        } catch (error) {
            console.error('❌ Ошибка загрузки RPC конфигурации:', error.message);
            throw error;
        }
    }

    /**
     * 🚀 ПОЛУЧЕНИЕ TRANSACTION RPC
     */
    getTransactionRPC() {
        if (!this.config.transaction.url) {
            throw new Error('❌ TRANSACTION RPC не настроен!');
        }
        return this.config.transaction;
    }

    /**
     * 📊 ПОЛУЧЕНИЕ DATA RPC ПРОВАЙДЕРОВ
     */
    getDataRPCs() {
        if (this.config.data.length === 0) {
            throw new Error('❌ Нет доступных DATA RPC провайдеров!');
        }
        return this.config.data.sort((a, b) => a.priority - b.priority);
    }

    /**
     * 🔑 ПОЛУЧЕНИЕ WALLET КОНФИГУРАЦИИ
     */
    getWalletConfig() {
        return this.config.wallet;
    }

    /**
     * 🏦 ПОЛУЧЕНИЕ MARGINFI КОНФИГУРАЦИИ
     */
    getMarginfiConfig() {
        return this.config.marginfi;
    }

    /**
     * 🌪️ ПОЛУЧЕНИЕ METEORA КОНФИГУРАЦИИ
     */
    getMeteoraConfig() {
        return this.config.meteora;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ METEORA POOL 1 (ОСНОВНОЙ)
     */
    getMeteoraPool1() {
        return this.config.meteora.pools.POOL_1;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ METEORA POOL 2 (РЕЗЕРВНЫЙ)
     */
    getMeteoraPool2() {
        return this.config.meteora.pools.POOL_2;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ EVENT AUTHORITY
     */
    getEventAuthority() {
        return this.config.meteora.eventAuthority;
    }

    // �️ МЕТОДЫ getPool1BinArrays() И getPool2BinArrays() УДАЛЕНЫ!
    // 🔥 ИСПОЛЬЗУЕМ ТОЛЬКО ДИНАМИЧЕСКУЮ ГЕНЕРАЦИЮ BIN ARRAYS!

    /**
     * 🔄 ПЕРЕЗАГРУЗКА КОНФИГУРАЦИИ
     */
    reload() {
        console.log('🔄 Перезагрузка RPC конфигурации...');
        this.loadConfig();
    }

    /**
     * 📊 СТАТИСТИКА КОНФИГУРАЦИИ
     */
    getStats() {
        return {
            transactionRPC: this.config.transaction.name,
            dataRPCs: this.config.data.length,
            enabledProviders: this.config.data.map(p => p.name),
            walletConfigured: !!this.config.wallet.address
        };
    }
}

// 🔥 ЭКСПОРТ КЛАССА И СИНГЛТОНА
const rpcConfig = new RPCConfig();
module.exports = rpcConfig;
module.exports.RPCConfig = RPCConfig;
