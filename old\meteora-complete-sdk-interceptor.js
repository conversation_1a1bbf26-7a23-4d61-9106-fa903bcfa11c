/**
 * 🕵️ METEORA COMPLETE SDK INTERCEPTOR
 * МАКСИМАЛЬНО ДЕТАЛЬНЫЙ ПЕРЕХВАТ КАЖДОГО ВЫЗОВА SDK
 * Записывает ВСЕ параметры для точного воспроизведения
 */

const { PublicKey, Connection, Keypair } = require('@solana/web3.js');
const fs = require('fs');
const path = require('path');

// === CONFIGURATION ===
const CONFIG = {
  METEORA_PROGRAM_ID: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  
  // Ваши целевые PDA
  TARGET_PDAS: {
    position: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
    bitmapExtension: '4Kq7Q1MrfgTTL2CwUKjHvJyFmuG8Pa6CJgDBom5QkUSH',
    remainingAccount: 'Uvi3SzhdL75CRSq5npUV8GFyyh7BAVhCrQYbcmXeJhw'
  },
  
  // Параметры для тестирования
  LB_PAIR: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
  USER: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'
};

// === ПОЛНЫЙ ПЕРЕХВАТЧИК ===
class CompleteSDKInterceptor {
  constructor() {
    this.allCalls = [];
    this.targetMatches = [];
    this.isActive = false;
    this.originalMethods = {};
    this.callCounter = 0;
  }

  /**
   * 🔧 УСТАНОВКА ПОЛНОГО ПЕРЕХВАТЧИКА
   */
  install() {
    console.log('🔧 УСТАНОВКА ПОЛНОГО ПЕРЕХВАТЧИКА SDK...');
    
    // Сохраняем оригинальные методы
    this.originalMethods.findProgramAddress = PublicKey.findProgramAddress;
    this.originalMethods.findProgramAddressSync = PublicKey.findProgramAddressSync;
    
    const self = this;
    
    // Перехватываем асинхронную версию
    PublicKey.findProgramAddress = async function(seeds, programId) {
      const startTime = process.hrtime.bigint();
      const result = await self.originalMethods.findProgramAddress.call(this, seeds, programId);
      const endTime = process.hrtime.bigint();
      
      self.recordCall(seeds, programId, result, 'async', startTime, endTime);
      return result;
    };
    
    // Перехватываем синхронную версию
    PublicKey.findProgramAddressSync = function(seeds, programId) {
      const startTime = process.hrtime.bigint();
      const result = self.originalMethods.findProgramAddressSync.call(this, seeds, programId);
      const endTime = process.hrtime.bigint();
      
      self.recordCall(seeds, programId, result, 'sync', startTime, endTime);
      return result;
    };
    
    this.isActive = true;
    console.log('✅ Полный перехватчик установлен');
  }

  /**
   * 📝 ЗАПИСЬ ВЫЗОВА С ПОЛНЫМИ ДЕТАЛЯМИ
   */
  recordCall(seeds, programId, result, callType, startTime, endTime) {
    if (!this.isActive) return;
    
    this.callCounter++;
    const [pda, bump] = result;
    const pdaStr = pda.toBase58();
    const programIdStr = programId.toBase58();
    
    // ПОЛНЫЙ анализ seeds
    const seedsAnalysis = this.completeSedsAnalysis(seeds);
    
    // Проверяем совпадения с целевыми PDA
    const targetMatch = Object.entries(CONFIG.TARGET_PDAS).find(([key, targetPda]) => 
      targetPda === pdaStr
    );
    
    // Получаем полный стек вызовов
    const fullStackTrace = this.getFullStackTrace();
    
    const callRecord = {
      // Метаданные вызова
      callId: this.callCounter,
      timestamp: Date.now(),
      callType,
      executionTime: Number(endTime - startTime), // в наносекундах
      
      // Результат
      programId: programIdStr,
      pda: pdaStr,
      bump,
      
      // Анализ совпадений
      isMeteoraPDA: programIdStr === CONFIG.METEORA_PROGRAM_ID,
      targetMatch: targetMatch ? targetMatch[0] : null,
      
      // ПОЛНЫЕ данные seeds
      seedsCount: seeds.length,
      seedsRaw: seedsAnalysis,
      
      // Воспроизводимые данные
      reproduction: {
        seedsHex: seeds.map(s => Buffer.isBuffer(s) ? s.toString('hex') : s.toString()),
        seedsBase64: seeds.map(s => Buffer.isBuffer(s) ? s.toString('base64') : Buffer.from(s.toString()).toString('base64')),
        programIdHex: programId.toBuffer().toString('hex'),
        expectedPda: pdaStr,
        expectedBump: bump
      },
      
      // Контекст выполнения
      stackTrace: fullStackTrace,
      
      // Дополнительная информация
      nodeVersion: process.version,
      platform: process.platform
    };
    
    this.allCalls.push(callRecord);
    
    // Если это совпадение с целевым PDA - особое внимание
    if (targetMatch) {
      this.targetMatches.push(callRecord);
      
      console.log(`\n🎯 ЦЕЛЕВОЕ СОВПАДЕНИЕ #${this.callCounter}: ${targetMatch[0].toUpperCase()}`);
      console.log(`   PDA: ${pdaStr}`);
      console.log(`   Bump: ${bump}`);
      console.log(`   Program: ${programIdStr}`);
      console.log(`   Seeds (${seeds.length}):`);
      
      seedsAnalysis.forEach((seed, i) => {
        console.log(`     [${i}] ${seed.interpretation.primary}`);
        console.log(`         Type: ${seed.type}, Length: ${seed.length}`);
        console.log(`         Hex: ${seed.hex}`);
        console.log(`         All interpretations: ${seed.interpretation.all.join(', ')}`);
      });
      
      console.log(`   Стек вызова:`);
      fullStackTrace.relevant.slice(0, 3).forEach(line => {
        console.log(`     ${line}`);
      });
    }
    
    // Логируем все Meteora вызовы
    else if (programIdStr === CONFIG.METEORA_PROGRAM_ID) {
      console.log(`\n🔍 Meteora PDA #${this.callCounter}: ${pdaStr.slice(0, 8)}...`);
      console.log(`   Seeds: ${seedsAnalysis.map(s => s.interpretation.primary).join(' + ')}`);
    }
  }

  /**
   * 🔬 ПОЛНЫЙ АНАЛИЗ SEEDS
   */
  completeSedsAnalysis(seeds) {
    return seeds.map((seed, index) => {
      const isBuffer = Buffer.isBuffer(seed);
      
      if (!isBuffer) {
        return {
          index,
          type: typeof seed,
          length: 0,
          hex: '',
          raw: seed,
          interpretation: {
            primary: `${typeof seed}(${seed})`,
            all: [`${typeof seed}(${seed})`]
          }
        };
      }
      
      const hex = seed.toString('hex');
      const length = seed.length;
      const raw = Array.from(seed);
      
      // ВСЕ возможные интерпретации
      const interpretations = [];
      
      // Как строка
      try {
        const str = seed.toString('utf8');
        if (/^[\x20-\x7E]+$/.test(str)) {
          interpretations.push(`"${str}"`);
        }
      } catch {}
      
      // Как числа разных типов
      if (length === 1) {
        interpretations.push(`u8(${seed[0]})`);
        interpretations.push(`i8(${seed.readInt8(0)})`);
      } else if (length === 2) {
        interpretations.push(`u16(${seed.readUInt16LE(0)})`);
        interpretations.push(`i16(${seed.readInt16LE(0)})`);
        interpretations.push(`u16_be(${seed.readUInt16BE(0)})`);
        interpretations.push(`i16_be(${seed.readInt16BE(0)})`);
      } else if (length === 4) {
        interpretations.push(`u32(${seed.readUInt32LE(0)})`);
        interpretations.push(`i32(${seed.readInt32LE(0)})`);
        interpretations.push(`u32_be(${seed.readUInt32BE(0)})`);
        interpretations.push(`i32_be(${seed.readInt32BE(0)})`);
        interpretations.push(`f32(${seed.readFloatLE(0)})`);
      } else if (length === 8) {
        try {
          interpretations.push(`u64(${seed.readBigUInt64LE(0)})`);
          interpretations.push(`i64(${seed.readBigInt64LE(0)})`);
          interpretations.push(`u64_be(${seed.readBigUInt64BE(0)})`);
          interpretations.push(`i64_be(${seed.readBigInt64BE(0)})`);
          interpretations.push(`f64(${seed.readDoubleLE(0)})`);
        } catch {}
      }
      
      // Как PublicKey
      if (length === 32) {
        try {
          const pubkey = new PublicKey(seed);
          interpretations.push(`PublicKey(${pubkey.toBase58()})`);
        } catch {}
      }
      
      // Как hex
      interpretations.push(`0x${hex}`);
      
      // Как base64
      interpretations.push(`base64(${seed.toString('base64')})`);
      
      return {
        index,
        type: 'Buffer',
        length,
        hex,
        raw,
        interpretation: {
          primary: interpretations[0] || `${length}bytes(${hex})`,
          all: interpretations
        }
      };
    });
  }

  /**
   * 📍 ПОЛНЫЙ СТЕК ВЫЗОВОВ
   */
  getFullStackTrace() {
    const error = new Error();
    const stack = error.stack ? error.stack.split('\n').slice(1) : [];
    
    const relevant = stack.filter(line => 
      !line.includes('CompleteSDKInterceptor') && 
      !line.includes('findProgramAddress') &&
      !line.includes('node_modules/@solana/web3.js') &&
      line.trim().length > 0
    );
    
    return {
      full: stack,
      relevant,
      nodeModules: stack.filter(line => line.includes('node_modules')),
      userCode: stack.filter(line => 
        !line.includes('node_modules') && 
        !line.includes('CompleteSDKInterceptor') &&
        line.trim().length > 0
      )
    };
  }

  /**
   * 🛑 УДАЛЕНИЕ ПЕРЕХВАТЧИКА
   */
  uninstall() {
    if (this.isActive) {
      PublicKey.findProgramAddress = this.originalMethods.findProgramAddress;
      PublicKey.findProgramAddressSync = this.originalMethods.findProgramAddressSync;
      this.isActive = false;
      console.log('🛑 Полный перехватчик удален');
    }
  }

  /**
   * 📊 ГЕНЕРАЦИЯ ПОЛНОГО ОТЧЕТА
   */
  generateReport() {
    const meteoraCalls = this.allCalls.filter(call => call.isMeteoraPDA);
    
    return {
      summary: {
        totalCalls: this.allCalls.length,
        meteoraCalls: meteoraCalls.length,
        targetMatches: this.targetMatches.length,
        executionTime: {
          total: this.allCalls.reduce((sum, call) => sum + call.executionTime, 0),
          average: this.allCalls.length > 0 ? 
            this.allCalls.reduce((sum, call) => sum + call.executionTime, 0) / this.allCalls.length : 0
        }
      },
      targetMatches: this.targetMatches,
      meteoraCalls,
      allCalls: this.allCalls,
      reproduction: {
        instructions: "Используйте данные из reproduction для точного воспроизведения",
        example: this.targetMatches.length > 0 ? this.generateReproductionCode(this.targetMatches[0]) : null
      }
    };
  }

  /**
   * 🔄 ГЕНЕРАЦИЯ КОДА ВОСПРОИЗВЕДЕНИЯ
   */
  generateReproductionCode(callRecord) {
    const seeds = callRecord.reproduction.seedsHex.map(hex => `Buffer.from('${hex}', 'hex')`);
    
    return {
      javascript: `
// Воспроизведение ${callRecord.targetMatch} PDA
const seeds = [
  ${seeds.join(',\n  ')}
];
const programId = new PublicKey('${callRecord.programId}');
const [pda, bump] = PublicKey.findProgramAddressSync(seeds, programId);
// Ожидаемый результат: ${callRecord.pda} (bump: ${callRecord.bump})
      `.trim(),
      
      seedsAnalysis: callRecord.seedsRaw.map(seed => ({
        interpretation: seed.interpretation.primary,
        allOptions: seed.interpretation.all
      }))
    };
  }

  /**
   * 💾 ЭКСПОРТ ПОЛНОГО ОТЧЕТА
   */
  exportReport() {
    const report = this.generateReport();
    
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `meteora-complete-intercept-${timestamp}.json`;
    const filePath = path.join(outputDir, filename);
    
    fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
    
    console.log(`\n💾 ПОЛНЫЙ ОТЧЕТ СОХРАНЕН: ${filePath}`);
    
    // Также сохраняем код воспроизведения отдельно
    if (report.targetMatches.length > 0) {
      const reproductionCode = report.targetMatches.map(match => 
        this.generateReproductionCode(match).javascript
      ).join('\n\n');
      
      const codeFilePath = path.join(outputDir, `reproduction-code-${timestamp}.js`);
      fs.writeFileSync(codeFilePath, reproductionCode);
      
      console.log(`💾 КОД ВОСПРОИЗВЕДЕНИЯ: ${codeFilePath}`);
    }
    
    return filePath;
  }
}

// === ТЕСТИРОВАНИЕ ===
class ComprehensiveTester {
  
  /**
   * 🧪 КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ
   */
  static async runComprehensiveTests() {
    console.log('\n🧪 ЗАПУСК КОМПЛЕКСНОГО ТЕСТИРОВАНИЯ...');
    
    const lbPair = new PublicKey(CONFIG.LB_PAIR);
    const user = new PublicKey(CONFIG.USER);
    const programId = new PublicKey(CONFIG.METEORA_PROGRAM_ID);
    
    // Тестируем все возможные комбинации
    await this.testAllPositionCombinations(lbPair, user, programId);
    await this.testAllBitmapCombinations(lbPair, programId);
    await this.testAllBinArrayCombinations(lbPair, programId);
    await this.testMiscellaneousPDAs(lbPair, user, programId);
  }
  
  static async testAllPositionCombinations(lbPair, user, programId) {
    const prefixes = ['position', 'Position', 'user_position', 'lb_position', 'pos'];
    const orders = [
      (p, lp, u, i) => [p, lp, u, i],
      (p, lp, u, i) => [p, u, lp, i],
      (p, lp, u, i) => [p, lp, u],
      (p, lp, u, i) => [p, u, lp]
    ];
    
    for (const prefix of prefixes) {
      for (const order of orders) {
        for (let i = 0; i < 20; i++) {
          const encodings = [
            this.encodeU8(i),
            this.encodeU16(i),
            this.encodeU32(i),
            this.encodeU64(i),
            this.encodeI16(i),
            this.encodeI32(i),
            this.encodeI64(i)
          ];
          
          for (const encoding of encodings) {
            if (encoding === null) continue;
            try {
              const seeds = order(Buffer.from(prefix), lbPair.toBuffer(), user.toBuffer(), encoding);
              PublicKey.findProgramAddressSync(seeds.filter(Boolean), programId);
            } catch (e) {}
          }
        }
      }
    }
  }
  
  static async testAllBitmapCombinations(lbPair, programId) {
    const prefixes = ['bitmap_extension', 'BitmapExtension', 'bin_array_bitmap_extension', 'bitmap'];
    const binIds = Array.from({length: 200}, (_, i) => i - 100); // -100 to 99
    
    for (const prefix of prefixes) {
      for (const binId of binIds) {
        const alignments = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512];
        
        for (const alignment of alignments) {
          const aligned = Math.floor(binId / alignment) * alignment;
          const encodings = [
            this.encodeI16(aligned),
            this.encodeU16(aligned),
            this.encodeI32(aligned),
            this.encodeU32(aligned)
          ];
          
          for (const encoding of encodings) {
            if (encoding === null) continue;
            try {
              PublicKey.findProgramAddressSync([
                Buffer.from(prefix),
                lbPair.toBuffer(),
                encoding
              ], programId);
            } catch (e) {}
          }
        }
      }
    }
  }
  
  static async testAllBinArrayCombinations(lbPair, programId) {
    const prefixes = ['bin_array', 'BinArray', 'bin', 'array'];
    
    for (const prefix of prefixes) {
      for (let index = -200; index <= 200; index++) {
        const encodings = [
          this.encodeI64(index),
          this.encodeU64(index),
          this.encodeI32(index),
          this.encodeU32(index)
        ];
        
        for (const encoding of encodings) {
          if (encoding === null) continue;
          try {
            PublicKey.findProgramAddressSync([
              Buffer.from(prefix),
              lbPair.toBuffer(),
              encoding
            ], programId);
          } catch (e) {}
        }
      }
    }
  }
  
  static async testMiscellaneousPDAs(lbPair, user, programId) {
    // Event Authority
    const eventPrefixes = ['__event_authority', 'event_authority', 'EventAuthority'];
    for (const prefix of eventPrefixes) {
      try {
        PublicKey.findProgramAddressSync([Buffer.from(prefix)], programId);
      } catch (e) {}
    }
    
    // Reserve
    const tokens = [
      'So11111111111111111111111111111111111111112',
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    ];
    
    for (const tokenStr of tokens) {
      const token = new PublicKey(tokenStr);
      try {
        PublicKey.findProgramAddressSync([
          Buffer.from('reserve'),
          lbPair.toBuffer(),
          token.toBuffer()
        ], programId);
      } catch (e) {}
    }
  }
  
  // Утилиты кодирования
  static encodeU8(value) {
    return Buffer.from([value & 0xFF]);
  }
  
  static encodeU16(value) {
    if (value < 0 || value > 65535) return null;
    const buf = Buffer.alloc(2);
    buf.writeUInt16LE(value, 0);
    return buf;
  }

  static encodeI16(value) {
    if (value < -32768 || value > 32767) return null;
    const buf = Buffer.alloc(2);
    buf.writeInt16LE(value, 0);
    return buf;
  }

  static encodeU32(value) {
    if (value < 0 || value > 4294967295) return null;
    const buf = Buffer.alloc(4);
    buf.writeUInt32LE(value, 0);
    return buf;
  }

  static encodeI32(value) {
    if (value < -2147483648 || value > 2147483647) return null;
    const buf = Buffer.alloc(4);
    buf.writeInt32LE(value, 0);
    return buf;
  }

  static encodeU64(value) {
    if (value < 0) return null;
    const buf = Buffer.alloc(8);
    buf.writeBigUInt64LE(BigInt(value), 0);
    return buf;
  }

  static encodeI64(value) {
    const buf = Buffer.alloc(8);
    buf.writeBigInt64LE(BigInt(value), 0);
    return buf;
  }
}

// === MAIN EXECUTION ===
async function main() {
  console.log('🕵️ METEORA COMPLETE SDK INTERCEPTOR');
  console.log('МАКСИМАЛЬНО ДЕТАЛЬНЫЙ ПЕРЕХВАТ КАЖДОГО ВЫЗОВА');
  console.log('='.repeat(60));
  
  const interceptor = new CompleteSDKInterceptor();
  
  try {
    // Устанавливаем перехватчик
    interceptor.install();
    
    // Запускаем комплексное тестирование
    await ComprehensiveTester.runComprehensiveTests();
    
    // Генерируем и экспортируем отчет
    const reportPath = interceptor.exportReport();
    
    const report = interceptor.generateReport();
    console.log('\n📊 ИТОГОВАЯ СТАТИСТИКА:');
    console.log('='.repeat(40));
    console.log(`Всего вызовов: ${report.summary.totalCalls}`);
    console.log(`Meteora PDA: ${report.summary.meteoraCalls}`);
    console.log(`Целевых совпадений: ${report.summary.targetMatches}`);
    console.log(`Среднее время выполнения: ${(report.summary.executionTime.average / 1000000).toFixed(2)} мс`);
    
    if (report.summary.targetMatches > 0) {
      console.log('\n🎯 ВСЕ ЦЕЛЕВЫЕ PDA НАЙДЕНЫ И ЗАПИСАНЫ!');
      console.log('Проверьте файлы с кодом воспроизведения.');
    }
    
    console.log('\n✅ ПОЛНЫЙ ПЕРЕХВАТ ЗАВЕРШЕН!');
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  } finally {
    interceptor.uninstall();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  CompleteSDKInterceptor,
  ComprehensiveTester,
  CONFIG
};
